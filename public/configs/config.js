// cas服务地址
$casUrl = 'https://biyi-template-cas.ctbiyi.cn';

// 文件地址
// $fileUrl = 'http://*************:59018'; // 已经废弃，改成后端返回完整的url
$fileUrl = '';

// 后端接口地址
//$baseUrl = 'https://**************:9003';
// $baseUrl = 'http://localhost:30000';
// $baseUrl = 'http://************:30000';
$baseUrl = 'http://***********:18888/';

// 后端接口前缀
//$authAPIUrl = '/doiov-monitor-websrv'; // 本系统(电池)
$authAPIUrl = '/doiov-ivsmp-web'; // 本系统(电池)
$prefixAPI = '/api'; // 本系统(电池)的 api 前缀
$authAPIUrlBase = '/doiov-system-auth'; // 基座
$prefixAPIBase = '/api'; // 基座的 api 前缀

// biyi-auth后端地址
// $biyiAuthAPIUrl = 'https://**************:9003/doiov-system-auth';
$biyiAuthAPIUrl = 'http://*************:9004/doiov-system-auth';
// $biyiAuthAPIUrl = 'http://************:8888/doiov-system-auth';
// $biyiAuthAPIUrl = '/mock-service'; // 使用 mock-service 的模拟数据

$publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC0Jr1NzVUQMburkZT6Rkt0eaPmH8TN6E258l2tZMJgVCP/sL4oKjroKYmNPBkSSiLKFr9wwJqfesMeef6ChGRUXjG6DX0oxQRe0f5/UnyEm/NicJwz9xwkU34gbuo1VB/EA2QZ5dl1rj9iSsiqKLK6/QFlVuzslRdAXYZC79vprwIDAQAB';
