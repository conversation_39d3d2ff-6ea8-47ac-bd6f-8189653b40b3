// 配置项 https://prettier.io/docs/en/options.html
module.exports = {
  tabWidth: 2, // 一个tab等于两个空格
  useTabs: false, // 不使用tab
  semi: true, // 句末加分号
  singleQuote: true, // 用单引号
  trailingComma: 'es5', // 最后一个对象元素加逗号
  bracketSpacing: true, // 对象，数组加空格
  arrowParens: 'always', // (x) => {} 是否要有小括号. always: 是, avoid: 否
  printWidth: 100, // 换行字符串阈值
  endOfLine: 'lf', // 换行符
  htmlWhitespaceSensitivity: 'ignore',
};
