import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import autoprefixer from 'autoprefixer';
import { createHtmlPlugin } from 'vite-plugin-html';
import path from 'path';
import qiankun from 'vite-plugin-qiankun';

const config = ({ mode }) => {
  const isProd = mode === 'production';
  const envPrefix = 'APP_';
  const { APP_TITLE = '', APP_SYSTEM_CODE } = loadEnv(mode, process.cwd(), envPrefix);
  let base = '/';
  if ( isProd ) {
    base = `/${APP_SYSTEM_CODE}/`;
  }
  return {
    base,
    plugins: [
      vue(),
      qiankun(APP_SYSTEM_CODE, {
        useDevMode: true,
      }),
      createHtmlPlugin({
        minify: isProd,
        inject: {
          data: {
            title: APP_TITLE,
          },
        },
      }),
    ],
    build: {
      target: 'es2015',
      outDir: path.resolve(__dirname, 'dist'),
      assetsDir: 'assets',
      assetsInlineLimit: 8192,
      sourcemap: !isProd,
      emptyOutDir: true,
      rollupOptions: {
        input: path.resolve(__dirname, 'index.html'),
        output: {
          chunkFileNames: 'js/[name].[hash].js',
          entryFileNames: 'js/[name].[hash].js',
          // assetFileNames: "assets/[name].[hash].[ext]",
        },
      },
    },
    envPrefix,
    resolve: {
      alias: [
        { find: /^@\//, replacement: `${path.resolve(__dirname, 'src')}/` },
        { find: /^~@\//, replacement: `${path.resolve(__dirname, 'src')}/` },
        { find: /^~/, replacement: '' },
      ],
      extensions: ['.js', '.mjs', '.vue', '.json', '.less', '.css'],
    },
    css: {
      postcss: {
        plugins: [autoprefixer],
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          additionalData: `@import "${path.resolve(__dirname, 'src/styles/variable.less')}";`,
        },
      },
    },
    define: {
      'process.env': {
        BASE_API: '', // [MEMO VU3升级] 在 public\configs\config.js 中设置具体的值
        APP_SYSTEM_CODE,
      },
    },
    ...getServerConfig(),
  };
};


function getServerConfig() {
  return {
    server: {
      host: '0.0.0.0',
      open: true,
      port: 8081,
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
      proxy: {
        '/api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
        },
      },
    },
    preview: {
      port: 5000,
    },
  };
}

export default defineConfig(config);
