<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form
        v-if="!isLoadingGetDetail"
        ref="detailForm"
        :model="detailData"
        :rules="detailFormRules"
        label-colon
        :label-width="120"
      >
        <Card dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.品牌.label" :prop="formItemsConfig.品牌.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.品牌.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.车型.label" :prop="formItemsConfig.车型.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.车型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.车架号.label" :prop="formItemsConfig.车架号.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.车架号.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.受攻击设备.label"
                :prop="formItemsConfig.受攻击设备.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.受攻击设备.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.事件类型.label"
                :prop="formItemsConfig.事件类型.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.事件类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.威胁等级.label"
                :prop="formItemsConfig.威胁等级.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.威胁等级.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                  :label="formItemsConfig.攻击来源.label"
                  :prop="formItemsConfig.攻击来源.prop"
              >
                <Input
                    v-model.trim="detailData[formItemsConfig.攻击来源.prop]"
                    :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                  :label="formItemsConfig.攻击地区.label"
                  :prop="formItemsConfig.攻击地区.prop"
              >
                <Input
                    v-model.trim="detailData[formItemsConfig.攻击地区.prop]"
                    :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                  :label="formItemsConfig.受攻击地址.label"
                  :prop="formItemsConfig.受攻击地址.prop"
              >
                <Input
                    v-model.trim="detailData[formItemsConfig.受攻击地址.prop]"
                    :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                  :label="formItemsConfig.受攻击地区.label"
                  :prop="formItemsConfig.受攻击地区.prop"
              >
                <Input
                    v-model.trim="detailData[formItemsConfig.受攻击地区.prop]"
                    :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.事件时间.label"
                :prop="formItemsConfig.事件时间.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.事件时间.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem
                :label="formItemsConfig.事件详情.label"
                :prop="formItemsConfig.事件详情.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.事件详情.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem
                :label="formItemsConfig.日志详情.label"
                :prop="formItemsConfig.日志详情.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.日志详情.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">
            保存
          </Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';

import mixinsPageForm from '@/mixins/mixinsPageForm';
import { getDetail } from '@/api/audit';
import { isNonNullNumber } from '@/libs/lan';
import {
  LENGTH_MAX_INPUT,
  LENGTH_MAX_INPUT_LONG,
  RANGE_MAX_FLOAT,
  RANGE_MIN_FLOAT,
  MSG_RANGE_INTEGER,
  MSG_RANGE_FLOAT,
} from '@/define';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getDetail,
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'audit-list',
        },
      },
      // 页面配置-表单
      pageItemsConfig: {
        // --- 基础信息 ---
        品牌: {
          editFormItem: {
            valuekey: 'brandName',
          },
        },
        车型: {
          editFormItem: {
            valuekey: 'modelName',
          },
        },
        车架号: {
          editFormItem: {
            valuekey: 'vin',
          },
        },
        受攻击设备: {
          editFormItem: {
            valuekey: 'attackedDevice',
          },
        },
        事件类型: {
          editFormItem: {
            valuekey: 'eventDetail',
          },
        },
        威胁等级: {
          editFormItem: {
            valuekey: 'threatLevel',
          },
        },
        攻击来源: {
          editFormItem: {
            valuekey: 'attackSource',
          },
        },
        攻击地区: {
          editFormItem: {
            valuekey: 'attackSourceArea',
          },
        },
        受攻击地址: {
          editFormItem: {
            valuekey: 'attackDest',
          },
        },
        受攻击地区: {
          editFormItem: {
            valuekey: 'attackDestArea',
          },
        },
        事件时间: {
          editFormItem: {
            valuekey: 'eventTime',
          },
        },
        事件详情: {
          editFormItem: {
            valuekey: 'eventDetail',
          },
        },
        日志详情: {
          editFormItem: {
            valuekey: 'detail',
          },
        },
      },
    };
  },
  created() {
    this.initDetailPage();

    console.log(this.detailFormConfig);
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
