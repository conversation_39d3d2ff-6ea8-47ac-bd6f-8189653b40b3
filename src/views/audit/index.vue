<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
        ref="searchForm"
        :searchFormInitData="searchFormInitData"
        :searchFormListOrder="searchFormListOrder"
        :searchFormItems="searchFormItems"
        @on-search="handleSearch"
        @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div class="content-table">
          <Table
            ref="listTable"
            :columns="listColumns"
            :data="listData"
            :loading="listLoading"
            stripe
          >
            <template v-slot:action="{ row }">
              <span
                v-if="pagePermission.showListItem"
                class="action-btn"
                @click="showListItem({ record: row, isReadonly: true })"
              >
                详情
              </span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
          :total="listConfig.total"
          :model-value="listConfig.page"
          :page-size="listConfig.size"
          show-sizer
          transfer
          show-elevator
          show-total
          @on-page-size-change="changeSize"
          @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getTableList } from '@/api/audit';
import dayjs from 'dayjs';
import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getTableList,
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'audit-detail',
          permCode: 'ivsmp.audit.audit-detail',
        },
      },

      // 页面配置-列表、搜索
      searchFormListOrder: [
        '品牌',
        '车型',
        '车架号',
        '攻击IP',
        '受攻击设备',
        '事件类型',
        '事件时间',
      ],

      brandList: [],
      modelList: [],
      deviceList: [
        { label: 'IVI' },
        { label: 'T-BOX' },
        { label: 'GW' },
        { label: 'UDS' },
        { label: 'OBD' },
        { label: 'ADAS' },
        { label: 'TSP' },
        { label: 'CS' },
        { label: 'OTA' },
        { label: 'App' },
      ],
      eventTypes: [
        { label: '泛洪攻击' },
        { label: '协议漏洞利用' },
        { label: '中间人攻击' },
        { label: '隐蔽通道' },
        { label: '路由欺骗' },
        { label: '分片攻击' },
        { label: '时序攻击' },
        { label: '隧道滥用' },
      ],
      threatLevel: [{ label: 'LOW' }, { label: 'MEDIUM' }, { label: 'HIGH' }, { label: 'URGENT' }],
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['品牌', '车型', '车架号', '受攻击设备', '攻击来源', '攻击地区', '事件类型', '威胁等级', '事件时间'];
      if (this.pagePermission.showListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
    pageItemsConfig() {
      return {
        品牌: {
          searchFormItem: {
            valuekey: 'brandId',
            formItemType: 'Select',
            optionList: this.brandList,
          },
          listColumn: {
            valuekey: 'brandName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        车型: {
          searchFormItem: {
            valuekey: 'carModelId',
            formItemType: 'Select',
            optionList: this.modelList,
          },
          listColumn: {
            valuekey: 'modelName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        车架号: {
          searchFormItem: {
            valuekey: 'vin',
            dicType: 1307,
          },
          listColumn: {
            valuekey: 'vin',
            minWidth: 80,
          },
        },
        攻击IP: {
          searchFormItem: {
            valuekey: 'attackSource',
          },
          listColumn: {
            valuekey: 'attackSource',
            minWidth: 80,
          },
        },
        受攻击设备: {
          searchFormItem: {
            valuekey: 'attackedDevice',
            formItemType: 'Select',
            optionList: this.deviceList,
          },
          listColumn: {
            valuekey: 'attackedDevice',
            minWidth: 80,
          },
        },
        攻击来源: {
          searchFormItem: {
            valuekey: 'attackSource',
          },
          listColumn: {
            valuekey: 'attackSource',
            minWidth: 120,
          },
        },
        攻击地区: {
          searchFormItem: {
            valuekey: 'attackSourceArea',
          },
          listColumn: {
            valuekey: 'attackSourceArea',
            minWidth: 80,
          },
        },
        威胁等级: {
          searchFormItem: {
            valuekey: 'threatLevel',
            formItemType: 'Select',
            optionList: this.threatLevel,
          },
          listColumn: {
            valuekey: 'threatLevel',
            minWidth: 80,
          },
        },
        事件类型: {
          searchFormItem: {
            valuekey: 'ruleName',
            formItemType: 'Select',
            optionList: this.eventTypes,
          },
          listColumn: {
            valuekey: 'ruleName',
            minWidth: 80,
          },
        },
        事件时间: {
          searchFormItem: {
            valuekey: 'eventTime',
            formItemType: 'DateTimeRange',
          },
          listColumn: {
            valuekey: 'eventTime',
            width: 170,
            renderType: 'YMDHMS',
            // render: (h, params) => dayjs(params.row.eventTime).format('YYYY-MM-DD HH:mm:ss'),
          },
        },
      };
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initData();
  },
  methods: {
    async initData() {
      await this.getBrand();
      await this.getModel();

      this.initPage();
    },
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach((item) => {
          item.value = item.id;
          item.label = item.name;
        });
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res;
        this.modelList.forEach((item) => {
          item.value = item.id;
          item.label = item.modelName;
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@/styles/list-search-page.less";
</style>
