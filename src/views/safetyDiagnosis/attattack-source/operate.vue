<template>
    <div>
      <typical-page-header :header-title="pageTitle" />
      <div class="form">
        <LoadingDetail :isLoading="isLoadingGetDetail" />
        <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
          <Card dis-hover :bordered="false">
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.规则名称.label" :prop="formItemsConfig.规则名称.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.规则名称.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.规则名称.placeholder"
                    :maxlength="formItemsConfig.规则名称.maxlength"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.告警级别.label" :prop="formItemsConfig.告警级别.prop">
                  <SelectDic
                    v-model.trim="detailData[formItemsConfig.告警级别.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.告警级别.placeholder"
                    :list="formItemsConfig.告警级别.list"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.触发规则.label" :prop="formItemsConfig.触发规则.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.触发规则.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.触发规则.placeholder"
                    :maxlength="formItemsConfig.触发规则.maxlength"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.分析范围.label" :prop="formItemsConfig.分析范围.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.分析范围.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.分析范围.placeholder"
                    :maxlength="formItemsConfig.分析范围.maxlength"
                  >
                    <template v-slot:append>
                      <span>分钟</span>
                    </template>
                  </Input>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.攻击阈值.label" :prop="formItemsConfig.攻击阈值.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.攻击阈值.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.攻击阈值.placeholder"
                    :maxlength="formItemsConfig.攻击阈值.maxlength"
                  >
                    <template #prepend>
                        <span>大于</span>
                    </template>
                    <template #append>
                        <span>次</span>
                    </template>
                  </Input>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.是否评估.label" :prop="formItemsConfig.是否评估.prop">
                  <SelectDic
                    v-model.trim="detailData[formItemsConfig.是否评估.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.是否评估.placeholder"
                    :list="formItemsConfig.是否评估.list"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="24">
                <FormItem :label="formItemsConfig.IP白名单.label" :prop="formItemsConfig.IP白名单.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.IP白名单.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.IP白名单.placeholder"
                    :maxlength="formItemsConfig.IP白名单.maxlength"
                    type="textarea"
                    :rows="5"
                  />
                </FormItem>
              </Col>
            </Row>
          </Card>
        </Form>
        <FooterToolbar>
          <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
            <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
          </template>
          <template v-else>
            <Button type="default" @click="goBack">取消</Button>
            <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
          </template>
        </FooterToolbar>
      </div>
    </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import mixinsPageForm from '@/mixins/mixinsPageForm';
import SelectDic from '@/components/select/select-dic.vue';
import { detailApi, addApi, editApi } from '@/api/safetyDiagnosis/attattack-source';
import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG } from '@/define';
import { formatFormResponseData } from '@/mixins/createFormItemConfig.js';

export default {
    mixins: [mixinsPageForm],
    components: { LoadingDetail, TypicalPageHeader, SelectDic },
    data() {
      return {
        // 通用-表单
        isLoadingGetDetail: true,
        detailData: {},
        detailFormConfig: {},
        isLoadingCommitDetail: false,
        // 页面配置-API
        pageConfigAPI: {
          getDetailData: {
            apiFun: detailApi,
          },
          addDetailData: {
            apiFun: addApi,
            successMsg: '添加成功',
          },
          editDetailData: {
            apiFun: editApi,
            successMsg: '修改成功',
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          goBack: {
            routerName: 'attattack-source-list',
          },
        },
        // 页面配置-表单
        pageItemsConfig: {
          规则名称: {
            editFormItem: {
              valuekey: 'ruleName',
              formRules: [
                { ruleType: 'required', ruleMessage: '请填写规则名称', ruleTrigger: 'blur' },
                { ruleType: 'length', max: LENGTH_MAX_INPUT }],
            },
          },
          告警级别: {
            editFormItem: {
              valuekey: 'alarmLevel',
              formRules: [{ ruleType: 'required', ruleMessage: '请选择告警级别' }],
              list: [
                {
                    label: '高',
                    value: '1'
                },
                {
                    label: '中',
                    value: '2'
                },
                {
                    label: '低',
                    value: '3'
                }
              ]
            },
          },
          触发规则: {
            editFormItem: {
              valuekey: 'cronExpression',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写触发规则',
                  ruleTrigger: 'blur'
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }
              ]
            },
          },
          分析范围: {
            editFormItem: {
              valuekey: 'analysisRange',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写分析范围',
                  ruleTrigger: 'blur',
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }
              ],
            },
          },
          攻击阈值: {
            editFormItem: {
              valuekey: 'attackThreshold',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写攻击阈值',
                  ruleTrigger: 'blur'
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }
              ],
            },
          },
          是否评估: {
            editFormItem: {
              valuekey: 'needEvaluation',
              formRules: [{ ruleType: 'required', ruleMessage: '请选择是否评估' }],
              list: [
                {
                  label: '是',
                  value: '1'
                },
                {
                  label: '否',
                  value: '2'
                }]
            },
          },
          IP白名单: {
            editFormItem: {
              valuekey: 'ipWhite',
              formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }],
            },
          }
        },
      };
    },
    created() {
      this.initDetailPage();
    },
    methods: {
      handleSubmitBefore(data) {
        data.ruleType = '1';
        return data;
      },
      getDetailAfter(data) {
        this.detailData.analysisRange = data.analysisRange?.toString();
        this.detailData.attackThreshold = data.attackThreshold?.toString();
      }
    }
  };
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
