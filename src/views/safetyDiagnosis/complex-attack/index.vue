<template>
    <div class="page-wrap">
      <div class="base-table">
        <!-- 搜索 -->
        <SearchForm
            ref="searchForm"
            :searchFormInitData="searchFormInitData"
            :searchFormListOrder="searchFormListOrder"
            :searchFormItems="searchFormItems"
            @on-search="handleSearch"
            @reset-search="handleReset"
        />
        <!-- 列表 -->
        <div class="content">
          <div class="content-btn">
            <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">新增</Button>
          </div>
          <div class="content-table">
            <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
              <template v-slot:action="{ row }">
                <span v-if="pagePermission.showListItem" class="action-btn" @click="showListItem({ record: row })">详情</span>
                <span v-if="pagePermission.editListItem" class="action-btn" @click="editListItem({ record: row })">编辑</span>
                <span v-if="pagePermission.delListItems" class="action-btn" @click="delListItem({ record: row })">删除</span>
              </template>
            </Table>
          </div>
        </div>
        <Row justify="end" type="flex" class="page">
          <Page
              :total="listConfig.total"
              :model-value="listConfig.page"
              :page-size="listConfig.size"
              show-sizer
              transfer
              show-elevator
              show-total
              @on-page-size-change="changeSize"
              @on-change="changePage"
          />
        </Row>
      </div>
    </div>
  </template>

<script>
  import dayjs from 'dayjs';
  import SearchForm from '@/components/form/search-form';
  import mixinsPageList from '@/mixins/mixinsPageList';
  import { getListApi, deleteApi } from '@/api/safetyDiagnosis/attattack-source';

  export default {
    mixins: [mixinsPageList],
    components: {
      SearchForm,
    },
    data() {
      return {
        // 通用-搜索
        searchFormInitData: {},
        // 通用-列表
        listLoading: true,
        listConfig: {
          total: 0,
          size: 20,
          page: 1,
        },
        listData: [],
        // 页面配置-API
        pageConfigAPI: {
          getList: {
            apiFun: getListApi,
          },
          delListItem: {
            apiFun: deleteApi,
            confirm: {
              content: '是否确认删除当前数据？',
              comments: '删除后不可恢复，如需再次使用，需重新添加！',
            },
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          addListItems: {
            routerName: 'complex-attack-add',
            permCode: 'ivsmp.complex-attack.add',
          },
          editListItem: {
            routerName: 'complex-attack-edit',
            permCode: 'ivsmp.complex-attack.edit',
          },
          delListItems: {
            routerName: 'complex-attack-del',
            permCode: 'ivsmp.complex-attack.del',
          },
          showListItem: {
            routerName: 'complex-attack-detail',
            permCode: 'ivsmp.complex-attack.detail',
          }
        },
        // 页面配置-列表、搜索
        searchFormListOrder: ['规则名称', '是否评估', '告警等级'],
        needEvaluation: [
            {
                label: '是',
                value: 1
            },
            {
                label: '否',
                value: 2
            },
        ],
        alarmLevel: [
            {
                label: '高',
                value: 1
            },
            {
                label: '中',
                value: 2
            },
            {
                label: '低',
                value: 3
            }
        ]
      };
    },
    computed: {
      listColumnsOrder() {
        let columns = ['规则ID', '规则名称', '分析范围', '告警等级', '是否评估', '创建时间'];
        if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
          columns.push('ACTION');
        }
        return columns;
      },
      pageItemsConfig() {
        return {
          规则ID: {
            listColumn: {
              valuekey: 'id',
              minWidth: 120,
              renderType: 'ellipsis'
            }
          },
          规则名称: {
            searchFormItem: {
              valuekey: 'ruleName'
            },
            listColumn: {
              valuekey: 'ruleName',
              minWidth: 120,
              renderType: 'ellipsis'
            }
          },
          分析范围: {
            listColumn: {
              valuekey: 'analysisRange',
              minWidth: 120,
              renderType: 'ellipsis',
              render: (h, params) => `${params.row.analysisRange}分钟`
            }
          },
          告警等级: {
            searchFormItem: {
              valuekey: 'alarmLevel',
              formItemType: 'Select',
              optionList: this.alarmLevel
            },
            listColumn: {
              valuekey: 'alarmLevel',
              minWidth: 120,
              renderType: 'ellipsis',
              render: (h, params) => {
                const res = this.alarmLevel.find(item => item.value == params.row.alarmLevel);
                if (res) {
                    return res.label;
                }
                return '-';
              }
            }
          },
          是否评估: {
            searchFormItem: {
              valuekey: 'needEvaluation',
              formItemType: 'Select',
              optionList: this.needEvaluation
            },
            listColumn: {
              valuekey: 'needEvaluation',
              minWidth: 120,
              renderType: 'ellipsis',
              render: (h, params) => this.needEvaluation.find(item => item.value == params.row.needEvaluation).label
            }
          },
          创建时间: {
            listColumn: {
              valuekey: 'createdAt',
              minWidth: 120,
              renderType: 'ellipsis',
              render: (h, params) => dayjs(params.row.createdAt).format('YYYY-MM-DD HH:mm:ss')
            }
          }
        };
      }
    },
    created() {
      this.initSearchFormData();
    },
    mounted() {
      this.initPage();
    },
  };
</script>

<style lang="less" scoped>
  @import '~@/styles/list-search-page.less';
</style>
