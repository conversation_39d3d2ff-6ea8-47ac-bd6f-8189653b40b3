<template>
    <div>
      <typical-page-header :header-title="pageTitle" />
      <div class="form">
        <LoadingDetail :isLoading="isLoadingGetDetail" />
        <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
          <Card dis-hover :bordered="false">
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.规则名称.label" :prop="formItemsConfig.规则名称.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.规则名称.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.规则名称.placeholder"
                    :maxlength="formItemsConfig.规则名称.maxlength"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.告警级别.label" :prop="formItemsConfig.告警级别.prop">
                  <SelectDic
                    v-model.trim="detailData[formItemsConfig.告警级别.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.告警级别.placeholder"
                    :list="formItemsConfig.告警级别.list"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.触发规则.label" :prop="formItemsConfig.触发规则.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.触发规则.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.触发规则.placeholder"
                    :maxlength="formItemsConfig.触发规则.maxlength"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.分析范围.label" :prop="formItemsConfig.分析范围.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.分析范围.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.分析范围.placeholder"
                    :maxlength="formItemsConfig.分析范围.maxlength"
                  >
                    <template v-slot:append>
                      <span>分钟</span>
                    </template>
                  </Input>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.是否评估.label" :prop="formItemsConfig.是否评估.prop">
                  <SelectDic
                    v-model.trim="detailData[formItemsConfig.是否评估.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.是否评估.placeholder"
                    :list="formItemsConfig.是否评估.list"
                  />
                </FormItem>
              </Col>
            </Row>
          </Card>
          <Card title="链路事件配置" dis-hover :bordered="false">
            <div style="text-align: right;margin-bottom: 10px;">
              <Button v-if="!detailFormConfig.isReadonly" type="primary" @click="handleAdd">添加</Button>
            </div>
            <Table ref="listTable" :columns="listColumns" :data="listData" stripe>
              <template v-slot:action="{ row }">
                <span v-for="(item, index) in operat" :key="index" @click="handleOperat(row, item.type)" style="cursor: pointer;">
                  <span v-if="item.type === 'up' && row.attackOrder != 1 && listData.length > 1" style="margin-right: 10px;" >
                    {{ item.text }}
                  </span>
                  <span v-if="item.type === 'down' && row.attackOrder != listData.length && listData.length > 1">
                    {{ item.text }}
                  </span>
                  <span v-if="item.type === 'del'" style="margin-right: 10px;" >
                    {{ item.text }}
                  </span>
                </span>
              </template>
            </Table>
          </Card>
        </Form>
        <FooterToolbar>
          <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
            <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
          </template>
          <template v-else>
            <Button type="default" @click="goBack">取消</Button>
            <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
          </template>
        </FooterToolbar>
      </div>
      <add-event-dialog ref="add-event" :rule-list="ruleList" @submit="getEvent" />
    </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import mixinsPageForm from '@/mixins/mixinsPageForm';
import SelectDic from '@/components/select/select-dic.vue';
import AddEventDialog from './components/AddEvent.vue';
import {
  detailApi,
  addApi,
  editApi,
  getRulesApi
} from '@/api/safetyDiagnosis/attattack-source';
import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG } from '@/define';

export default {
    mixins: [mixinsPageForm,],
    components: {
      LoadingDetail,
      TypicalPageHeader,
      SelectDic,
      AddEventDialog
    },
    data() {
      return {
        ruleList: [],
        // 通用-表单
        isLoadingGetDetail: true,
        detailData: {},
        detailFormConfig: {},
        isLoadingCommitDetail: false,
        // 页面配置-API
        pageConfigAPI: {
          getDetailData: {
            apiFun: detailApi,
          },
          addDetailData: {
            apiFun: addApi,
            successMsg: '添加成功',
          },
          editDetailData: {
            apiFun: editApi,
            successMsg: '修改成功',
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          goBack: {
            routerName: 'complex-attack-list',
          },
        },
        // 页面配置-表单
        pageItemsConfig: {
          规则名称: {
            editFormItem: {
              valuekey: 'ruleName',
              formRules: [
                { ruleType: 'required', ruleMessage: '请填写规则名称', ruleTrigger: 'blur' },
                { ruleType: 'length', max: LENGTH_MAX_INPUT }
              ],
            },
          },
          告警级别: {
            editFormItem: {
              valuekey: 'alarmLevel',
              formRules: [{
                ruleType: 'required',
                ruleMessage: '请选择告警级别',
              }],
              list: [
                {
                    label: '高',
                    value: '1'
                },
                {
                    label: '中',
                    value: '2'
                },
                {
                    label: '低',
                    value: '3'
                }
              ]
            },
          },
          触发规则: {
            editFormItem: {
              valuekey: 'cronExpression',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写触发规则',
                  ruleTrigger: 'blur'
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }
              ]
            },
          },
          分析范围: {
            editFormItem: {
              valuekey: 'analysisRange',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写分析范围',
                  ruleTrigger: 'blur',
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }
              ],
            },
          },
          是否评估: {
            editFormItem: {
              valuekey: 'needEvaluation',
              formRules: [{
                ruleType: 'required',
                ruleMessage: '请选择是否评估',
              }],
              list: [
                {
                  label: '是',
                  value: '1'
                },
                {
                  label: '否',
                  value: '2'
                }]
            },
          }
        },
        listData: [],
        operat: [
          {
            text: '删除',
            type: 'del'
          },
          {
            text: '上移',
            type: 'up'
          },
          {
            text: '下移',
            type: 'down'
          }
        ]
      };
    },
    computed: {
      listColumns() {
        const arr = [
          {
            title: '攻击顺序',
            key: 'attackOrder'
          },
          {
            title: '事件名称',
            key: 'eventId',
            render: (h, params) => {
              const res = this.ruleList.find(item => item.id === params.row.eventId);
              if (res) {
                return res.ruleName;
              }
              return '-';
            }
          },
          {
            title: '受攻击设备名',
            key: 'targetDeviceCode'
          },
        ];
        if (!this.detailFormConfig.isReadonly) {
          arr.push({
            title: '操作',
            key: 'handle',
            width: 200,
            slot: 'action'
          });
        }
        return arr;
      }
    },
    created() {
      this.getRules();
      this.initDetailPage();
    },
    methods: {
      handleAdd() {
        this.$refs['add-event'].handleOpen();
      },
      handleOperat(row, type) {
        if (type === 'up') {
          this.listData = this.handleUp(row);
        } else if (type === 'down') {
          this.listData = this.handleDown(row);
        } else {
          const index = this.listData.findIndex(item => item.attackOrder === row.attackOrder);
          this.listData.splice(index, 1);
          for (let i = index; i < this.listData.length; i++) {
            this.listData[i].attackOrder--;
          }
        }
      },
      handleUp(row) {
        const newArr = [...this.listData];
        const index = this.listData.findIndex(item => item.attackOrder === row.attackOrder);
        const prevIndex = index - 1;
        const currentOrder = newArr[index].attackOrder;
        const prevOrder = newArr[prevIndex].attackOrder;
        newArr[prevIndex].attackOrder = currentOrder;
        newArr[index].attackOrder = prevOrder;
        [newArr[index], newArr[prevIndex]] = [newArr[prevIndex], newArr[index]];
        return newArr;
      },
      handleDown(row) {
        const newArr = [...this.listData];
        const index = this.listData.findIndex(item => item.attackOrder === row.attackOrder);
        const nextIndex = index + 1;
        const currentOrder = newArr[index].attackOrder;
        const nextOrder = newArr[nextIndex].attackOrder;
        newArr[nextIndex].attackOrder = currentOrder;
        newArr[index].attackOrder = nextOrder;
        [newArr[index], newArr[nextIndex]] = [newArr[nextIndex], newArr[index]];
        return newArr;
      },
      async getRules() {
        const res = await getRulesApi({});
        if (res && res.length) {
          this.ruleList = res;
        }
      },
      getEvent(data) {
        const obj = JSON.parse(JSON.stringify(data));
        obj.attackOrder = this.listData.length + 1;
        this.listData.push(obj);
      },
      handleSubmitBefore(data) {
        data.ruleType = '3';
        data.complexAttackSequenceList = this.listData;
        return data;
      },
      getDetailAfter(data) {
        this.detailData.analysisRange = data.analysisRange.toString();
        if (data.complexAttackSequenceList.length) {
          this.listData = data.complexAttackSequenceList;
        }
      }
    }
  };
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
