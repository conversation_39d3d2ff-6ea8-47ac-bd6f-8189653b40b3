<template>
    <div>
      <Modal
        v-model="modal"
        title="复杂攻击事件选择"
        :loading="loading"
        footer-hide
        @on-ok="handleSubmit">
        <Form ref="form" :model="formData" :rules="rules" label-colon :label-width="100">
            <FormItem label="事件" prop="eventId">
                <Select
                    v-model="formData.eventId"
                    placeholder="请选择"
                >
                    <Option v-for="(item, index) in ruleList" :key="index" :value="item.id">
                        {{ item.ruleName }}
                    </Option>
                </Select>
            </FormItem>
            <FormItem label="目标设备" prop="targetDeviceCode">
                <Select
                    v-model="formData.targetDeviceCode"
                    placeholder="请选择"
                >
                    <Option v-for="(item, index) in deviceList" :key="index" :value="item.label">
                        {{ item.label }}
                    </Option>
                </Select>
            </FormItem>
        </Form>
        <div slot="footer" style="text-align: right;">
          <Button type="text" @click="handleClose">取消</Button>
          <Button type="primary" @click="handleSubmit">确定</Button>
        </div>
      </Modal>
    </div>
</template>

<script>
export default {
    props: {
        ruleList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            modal: false,
            loading: false,
            formData: {
                eventId: '',
                targetDeviceCode: ''
            },
            deviceList: [
                { label: 'IVI' },
                { label: 'T-BOX' },
                { label: 'GW' },
                { label: 'UDS' },
                { label: 'OBD' },
                { label: 'ADAS' },
                { label: 'TSP' },
                { label: 'CS' },
                { label: 'OTA' },
                { label: 'App' },
            ],
            rules: {
                eventId: [{ required: true, message: '请选择事件', trigger: 'change' }],
                targetDeviceCode: [{ required: true, message: '请选择目标设备', trigger: 'change' }]
            }
        };
    },
    methods: {
        handleOpen() {
            this.modal = true;
            this.reset();
        },
        handleClose() {
            this.modal = false;
        },
        reset() {
            this.formData = {
                eventId: '',
                targetDeviceCode: ''
            };
            this.$nextTick(() => {
                this.$refs.form && this.$refs.form.resetFields();
            });
        },
        handleSubmit() {
            return new Promise((resolve, reject) => {
                this.$refs.form.validate(valid => {
                    if (valid) {
                        this.$emit('submit', this.formData);
                        this.handleClose();
                        resolve();
                    } else {
                        this.$message.error('请完善表单信息');
                        this.loading = false;
                        reject();
                    }
                });
            });
        }
    }
};
</script>

<style>
</style>
