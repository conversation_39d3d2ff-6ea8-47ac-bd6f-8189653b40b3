<template>
    <div>
      <typical-page-header :header-title="pageTitle" />
      <div class="form">
        <LoadingDetail :isLoading="isLoadingGetDetail" />
        <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
          <Card dis-hover :bordered="false">
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.品牌.label" :prop="formItemsConfig.品牌.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.品牌.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.品牌.placeholder"
                    :maxlength="formItemsConfig.品牌.maxlength"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.代码.label" :prop="formItemsConfig.代码.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.代码.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.代码.placeholder"
                    :maxlength="formItemsConfig.代码.maxlength"
                  />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.备注.label" :prop="formItemsConfig.备注.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.备注.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.备注.placeholder"
                    :maxlength="formItemsConfig.备注.maxlength"
                    type="textarea"
                    :rows="5"
                  />
                </FormItem>
              </Col>
            </Row>
          </Card>
        </Form>
        <FooterToolbar>
          <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
            <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
          </template>
          <template v-else>
            <Button type="default" @click="goBack">取消</Button>
            <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
          </template>
        </FooterToolbar>
      </div>
    </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import mixinsPageForm from '@/mixins/mixinsPageForm';
import { detailApi, addApi, editApi } from '@/api/asset/car-brand';
import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG } from '@/define';

export default {
    mixins: [mixinsPageForm],
    components: { LoadingDetail, TypicalPageHeader },
    data() {
      return {
        // 通用-表单
        isLoadingGetDetail: true,
        detailData: {},
        detailFormConfig: {},
        isLoadingCommitDetail: false,
        // 页面配置-API
        pageConfigAPI: {
          getDetailData: {
            apiFun: detailApi,
          },
          addDetailData: {
            apiFun: addApi,
            successMsg: '品牌添加成功',
          },
          editDetailData: {
            apiFun: editApi,
            successMsg: '品牌修改成功',
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          goBack: {
            routerName: 'car-brand-list',
          },
        },
        // 页面配置-表单
        pageItemsConfig: {
          品牌: {
            editFormItem: {
              valuekey: 'name',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写品牌',
                  ruleTrigger: 'blur',
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT }
              ],
            },
          },
          代码: {
            editFormItem: {
              valuekey: 'code',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写代码',
                  ruleTrigger: 'blur',
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT }
              ],
            },
          },
          备注: {
            editFormItem: {
              valuekey: 'remark',
              formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }],
            },
          }
        },
      };
    },
    created() {
      this.initDetailPage();
    },
  };
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
