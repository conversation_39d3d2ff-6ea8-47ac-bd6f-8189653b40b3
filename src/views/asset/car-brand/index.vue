<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
          ref="searchForm"
          :searchFormInitData="searchFormInitData"
          :searchFormListOrder="searchFormListOrder"
          :searchFormItems="searchFormItems"
          @on-search="handleSearch"
          @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div class="content-btn">
          <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">新增</Button>
        </div>
        <div class="content-table">
          <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
            <template v-slot:action="{ row }">
              <span v-if="pagePermission.editListItem" class="action-btn" @click="editListItem({ record: row })">编辑</span>
              <span v-if="pagePermission.delListItems" class="action-btn" @click="delListItem({ record: row })">删除</span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
            :total="listConfig.total"
            :model-value="listConfig.page"
            :page-size="listConfig.size"
            show-sizer
            transfer
            show-elevator
            show-total
            @on-page-size-change="changeSize"
            @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getListApi, deleteApi } from '@/api/asset/car-brand';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 20,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getListApi,
        },
        delListItem: {
          apiFun: deleteApi,
          confirm: {
            content: '是否确认删除品牌？',
            comments: '删除后不可恢复，如需再次使用该品牌，需重新添加！',
          },
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        addListItems: {
          routerName: 'car-brand-add',
          permCode: 'ivsmp.asset-mange.car-brand-add',
        },
        editListItem: {
          routerName: 'car-brand-edit',
          permCode: 'ivsmp.asset-mange.car-brand-edit',
        },
        delListItems: {
          routerName: 'car-brand-del',
          permCode: 'ivsmp.asset-mange.car-brand-del',
        }
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['名称'],
      pageItemsConfig: {
        名称: {
          searchFormItem: {
            valuekey: 'name',
          },
          listColumn: {
            valuekey: 'name',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        代码: {
          listColumn: {
            valuekey: 'code',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        备注: {
          listColumn: {
            valuekey: 'remark',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        }
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['名称', '代码', '备注'];
      // if (this.pagePermission.delListItems) {
      //   columns.unshift('CHECKBOX');
      // }
      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
