<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
          ref="searchForm"
          colPerRow="3"
          :searchFormInitData="searchFormInitData"
          :searchFormListOrder="searchFormListOrder"
          :searchFormItems="searchFormItems"
          @on-search="handleSearch"
          @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div class="content-btn">
          <Upload
            ref="upload"
            v-if="pagePermission.uploadItems"
            :action="importApi"
            :format="['xlsx', 'xls']"
            style="margin-right: 10px;"
            :show-upload-list="false"
            :on-success="uploadSuccess">
            <Button type="primary">导入</Button>
          </Upload>
          <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">添加</Button>
        </div>
        <div class="content-table">
          <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
            <template v-slot:action="{ row }">
              <span v-if="pagePermission.editListItem" class="action-btn" @click="editListItem({ record: row })">修改</span>
              <span v-if="pagePermission.delListItems" class="action-btn" @click="delListItem({ record: row })">删除</span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
            :total="listConfig.total"
            :model-value="listConfig.page"
            :page-size="listConfig.size"
            show-sizer
            transfer
            show-elevator
            show-total
            @on-page-size-change="changeSize"
            @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getListApi, deleteApi, importApi } from '@/api/asset/car-list';
import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      importApi,
      brandList: [],
      modelList: [],
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getListApi,
        },
        delListItem: {
          apiFun: deleteApi,
          confirm: {
            content: '是否确认删除车辆信息？',
            comments: '删除后不可恢复，如需再次使用该数据，需重新添加！',
          }
        }
      },
      // 页面配置-Button
      pageConfigButton: {
        addListItems: {
          routerName: 'car-add',
          permCode: 'ivsmp.asset-mange.car-add',
        },
        editListItem: {
          routerName: 'car-edit',
          permCode: 'ivsmp.asset-mange.car-edit',
        },
        delListItems: {
          routerName: 'car-del',
          permCode: 'ivsmp.asset-mange.car-del',
        },
        showListItem: {
          routerName: 'car-detail',
          permCode: 'ivsmp.asset-mange.car-detail',
        },
        uploadItems: {
          permCode: 'ivsmp.asset-mange.car-upload',
        }
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['品牌', '车型', '车辆ID', '注册时间']
    };
  },
  computed: {
    listColumnsOrder() {
      let columns = ['品牌', '车型', '车辆ID', '注册时间', '今日被攻击次数', '上次登陆时间'];
      columns.push('ACTION');
      return columns;
    },
    pageItemsConfig() {
      return {
        品牌: {
          searchFormItem: {
            valuekey: 'brandId',
            formItemType: 'Select',
            optionList: this.brandList
          },
          listColumn: {
            valuekey: 'brandId',
            minWidth: 120,
            renderType: 'ellipsis',
            render: (h, params) => {
              const res = this.brandList.find(item => item.id === params.row.brandId);
              if (Object.keys(res).length) {
                return res.name;
              }
              return '--';
            }
          },
        },
        车型: {
          searchFormItem: {
            valuekey: 'carModelId',
            formItemType: 'Select',
            optionList: this.modelList
          },
          listColumn: {
            valuekey: 'carModelId',
            minWidth: 120,
            renderType: 'ellipsis',
            render: (h, params) => {
              const res = this.modelList.find(item => item.id === params.row.carModelId);
              if (Object.keys(res).length) {
                return res.modelName;
              }
              return params.row.carModelId;
            }
          },
        },
        车辆ID: {
          searchFormItem: {
            valuekey: 'vin',
          },
          listColumn: {
            valuekey: 'vin',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        注册时间: {
          searchFormItem: {
            valuekey: 'createdAt',
            format: 'yyyy-MM-dd HH:mm:ss',
            formItemType: 'DateTimeRange',
          },
          listColumn: {
            valuekey: 'createdAt',
            minWidth: 120,
            render: (h, params) => dayjs(params.row.createdAt).format('YYYY-MM-DD HH:mm:ss')
          },
        },
        今日被攻击次数: {
          listColumn: {
            valuekey: 'attackToday',
            minWidth: 120,
            renderType: 'ellipsis'
          },
        },
        上次登陆时间: {
          listColumn: {
            valuekey: 'lastLogin',
            minWidth: 120,
            renderType: 'ellipsis',
            render: (h, params) => dayjs(params.row.lastLogin).format('YYYY-MM-DD HH:mm:ss')
          },
        }
      };
    }
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
    this.getBrand();
    this.getModel();
  },
  methods: {
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach(item => {
          item.value = item.id;
          item.label = item.name;
        });
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res;
        this.modelList.forEach(item => {
          item.value = item.id;
          item.label = item.modelName;
        });
      }
    },
    uploadSuccess(e) {
      this.$Message.success('导入成功');
      this.refreshPage();
      this.$refs['upload'].clearFiles();
    }
  }
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
