<template>
    <div class="page-wrap">
      <div class="base-table">
        <!-- 搜索 -->
        <SearchForm
            ref="searchForm"
            :searchFormInitData="searchFormInitData"
            :searchFormListOrder="searchFormListOrder"
            :searchFormItems="searchFormItems"
            @on-search="handleSearch"
            @reset-search="handleReset"
        />
        <!-- 列表 -->
        <div class="content">
          <div class="content-btn">
            <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">添加</Button>
          </div>
          <div class="content-table">
            <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
              <template v-slot:action="{ row }">
                <span v-if="pagePermission.editListItem" class="action-btn" @click="editListItem({ record: row })">编辑</span>
                <span v-if="pagePermission.delListItems" class="action-btn" @click="delListItem({ record: row })">删除</span>
              </template>
            </Table>
          </div>
        </div>
        <Row justify="end" type="flex" class="page">
          <Page
              :total="listConfig.total"
              :model-value="listConfig.page"
              :page-size="listConfig.size"
              show-sizer
              transfer
              show-elevator
              show-total
              @on-page-size-change="changeSize"
              @on-change="changePage"
          />
        </Row>
      </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getListApi, deleteApi } from '@/api/asset/car-model';
import { getAllBrandApi } from '@/api/asset/car-brand';

  export default {
    mixins: [mixinsPageList],
    components: {
      SearchForm,
    },
    data() {
      return {
        brandList: [],
        // 通用-搜索
        searchFormInitData: {},
        // 通用-列表
        listLoading: true,
        listConfig: {
          total: 0,
          size: 10,
          page: 1,
        },
        listData: [],
        // 页面配置-API
        pageConfigAPI: {
          getList: {
            apiFun: getListApi,
          },
          delListItem: {
            apiFun: deleteApi,
            confirm: {
              content: '是否确认删除车型信息？',
              comments: '删除后不可恢复，如需再次使用该数据，需重新添加！',
            },
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          addListItems: {
            routerName: 'car-model-add',
            permCode: 'ivsmp.asset-mange.car-model-add',
          },
          editListItem: {
            routerName: 'car-model-edit',
            permCode: 'ivsmp.asset-mange.car-model-edit',
          },
          delListItems: {
            routerName: 'car-model-del',
            permCode: 'ivsmp.asset-mange.car-model-del',
          }
        },
        // 页面配置-列表、搜索
        searchFormListOrder: ['品牌']
      };
    },
    computed: {
      listColumnsOrder: function () {
        let columns = ['品牌', '车型', '车型代码', '责任部门', '责任人', 'SOP时间', '备注', '今日被攻击次数'];
        if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
          columns.push('ACTION');
        }
        return columns;
      },
      pageItemsConfig() {
        return {
          品牌: {
            searchFormItem: {
              valuekey: 'brandId',
              formItemType: 'Select',
              optionList: this.brandList
            },
            listColumn: {
              valuekey: 'brandId',
              minWidth: 120,
              render: (h, params) => {
                const res = this.brandList.find(item => item.id === params.row.brandId);
                if (Object.keys(res).length) {
                  return res.name;
                }
                return '--';
              }
            },
          },
          车型: {
            searchFormItem: {
              valuekey: 'modelName',
            },
            listColumn: {
              valuekey: 'modelName',
              minWidth: 120,
              renderType: 'ellipsis',
            },
          },
          车型代码: {
            listColumn: {
              valuekey: 'modelCode',
              minWidth: 120,
              renderType: 'ellipsis',
            },
          },
          责任部门: {
            listColumn: {
              valuekey: 'department',
              minWidth: 100,
              renderType: 'ellipsis',
            },
          },
          责任人: {
            listColumn: {
              valuekey: 'departmentManager',
              minWidth: 80,
              renderType: 'ellipsis',
            },
          },
          SOP时间: {
            listColumn: {
              valuekey: 'sopDate',
              minWidth: 120,
              renderType: 'ellipsis',
              render: (h, params) => dayjs(params.row.sopDate).format('YYYY-MM-DD HH:mm:ss')
            },
          },
          备注: {
            listColumn: {
              valuekey: 'remark',
              minWidth: 120,
              renderType: 'ellipsis',
            },
          },
          今日被攻击次数: {
            listColumn: {
              valuekey: 'attackToday',
              minWidth: 150,
              renderType: 'ellipsis',
            },
          }
        };
      }
    },
    created() {
      this.initSearchFormData();
    },
    mounted() {
      this.initPage();
      this.getBrand();
    },
    methods: {
      async getBrand() {
        const res = await getAllBrandApi({});
        if (res && res.length) {
          this.brandList = res;
          this.brandList.forEach(item => {
            item.value = item.id;
            item.label = item.name;
          });
        }
      }
    }
  };
</script>

<style lang="less" scoped>
  @import '~@/styles/list-search-page.less';
  </style>
