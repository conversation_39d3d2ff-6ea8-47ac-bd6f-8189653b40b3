<template>
    <div>
      <typical-page-header :header-title="pageTitle" />
      <div class="form">
        <LoadingDetail :isLoading="isLoadingGetDetail" />
        <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
          <Card dis-hover :bordered="false">
            <Row>
              <Col span="12">
                <FormItem :label="formItemsConfig.品牌.label" :prop="formItemsConfig.品牌.prop">
                  <Select
                    v-model="detailData[formItemsConfig.品牌.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.品牌.placeholder"
                    @on-change="change"
                  >
                    <Option v-for="(item, index) in brandList" :key="index" :value="item.id">{{ item.name }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.车型.label" :prop="formItemsConfig.车型.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.车型.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.车型.placeholder"
                    :maxlength="formItemsConfig.车型.maxlength"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.车型代码.label" :prop="formItemsConfig.车型代码.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.车型代码.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.车型代码.placeholder"
                    :maxlength="formItemsConfig.车型代码.maxlength"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.责任部门.label" :prop="formItemsConfig.责任部门.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.责任部门.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.责任部门.placeholder"
                    :maxlength="formItemsConfig.责任部门.maxlength"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.责任人.label" :prop="formItemsConfig.责任人.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.责任人.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.责任人.placeholder"
                    :maxlength="formItemsConfig.责任人.maxlength"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="formItemsConfig.SOP时间.label" :prop="formItemsConfig.SOP时间.prop">
                  <DatePicker
                    v-model="detailData[formItemsConfig.SOP时间.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    type="datetime"
                    :format="'yyyy-MM-dd HH:mm:ss'"
                    :editable="false"
                    placement="right"
                    :placeholder="formItemsConfig.SOP时间.placeholder"
                    @on-change="handleChange"
                  />
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem :label="formItemsConfig.备注.label" :prop="formItemsConfig.备注.prop">
                  <Input
                    v-model.trim="detailData[formItemsConfig.备注.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.备注.placeholder"
                    type="textarea"
                    :rows="2"
                    :maxlength="formItemsConfig.备注.maxlength"
                    show-word-limit
                  />
                </FormItem>
              </Col>
            </Row>
          </Card>
        </Form>
        <FooterToolbar>
          <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
            <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
          </template>
          <template v-else>
            <Button type="default" @click="goBack">取消</Button>
            <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
          </template>
        </FooterToolbar>
      </div>
    </div>
  </template>

  <script>
  import LoadingDetail from '@/components/loading/loading-detail.vue';
  import TypicalPageHeader from '@/components/typical-page-header/index.vue';

  import mixinsPageForm from '@/mixins/mixinsPageForm';
  import { detailApi, addApi, editApi } from '@/api/asset/car-model';
  import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG } from '@/define';
  import { getAllBrandApi } from '@/api/asset/car-brand';

  export default {
    mixins: [mixinsPageForm],
    components: { LoadingDetail, TypicalPageHeader },
    data() {
      return {
        // 通用-表单
        brandList: [],
        isLoadingGetDetail: true,
        detailData: {},
        detailFormConfig: {},
        isLoadingCommitDetail: false,
        // 页面配置-API
        pageConfigAPI: {
          getDetailData: {
            apiFun: detailApi,
          },
          addDetailData: {
            apiFun: addApi,
            successMsg: '车型添加成功',
          },
          editDetailData: {
            apiFun: editApi,
            successMsg: '车型修改成功',
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          goBack: {
            routerName: 'car-model-list',
          },
        },
      };
    },
    computed: {
      pageItemsConfig() {
        return {
          品牌: {
            editFormItem: {
              valuekey: 'brandId',
              formRules: [{
                ruleType: 'required',
                ruleMessage: '请选择品牌'
              }]
            },
          },
          车型: {
            editFormItem: {
              valuekey: 'modelName',
              formRules: [{
                ruleType: 'required',
                  ruleMessage: '请填写车型',
                  ruleTrigger: 'blur'
              },
              { ruleType: 'length', max: LENGTH_MAX_INPUT }
            ],
            },
          },
          车型代码: {
            editFormItem: {
              valuekey: 'modelCode',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写车型代码',
                  ruleTrigger: 'blur'
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT }
              ],
            },
          },
          责任部门: {
            editFormItem: {
              valuekey: 'department',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写责任部门',
                  ruleTrigger: 'blur'
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT }
              ],
            },
          },
          责任人: {
            editFormItem: {
              valuekey: 'departmentManager',
              formRules: [
                {
                  ruleType: 'required',
                  ruleMessage: '请填写责任人',
                  ruleTrigger: 'blur',
                },
                { ruleType: 'length', max: LENGTH_MAX_INPUT }
              ],
            },
          },
          SOP时间: {
            editFormItem: {
              valuekey: 'sopDate',
              formRules: [{ ruleType: 'required', ruleMessage: '请选择SOP时间' }]
            },
          },
          备注: {
            editFormItem: {
              valuekey: 'remark',
              formRules: [
                { formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }] }
              ],
            },
          }
        };
      }
    },
    created() {
      this.initDetailPage();
      this.getBrand();
    },
    methods: {
      async getBrand() {
        const res = await getAllBrandApi({});
        if (res && res.length) {
          this.brandList = res;
        }
      },
      handleChange(e) {
        this.detailData.sopDate = e;
      },
      change(e) {
        this.detailData.brandId = e;
      }
    }
  };
  </script>

  <style lang="less" scoped>
  @import '~@/styles/form-page.less';
  </style>
