<template>
  <div class="i-table-no-border">
    <Card :bordered="false" dis-hover>
      <template v-slot:title>
        <div>
          <Avatar v-color="'#2f54eb'" v-bg-color="'#f0f5ff'" icon="md-locate" size="small" />
          <span class="ivu-pl-8">前端日志</span>
        </div>
      </template>
      <template v-slot:extra>
        <div>
          <Tooltip content="清空日志" placement="top">
            <Button type="text" @click="clean">
              <Icon type="md-trash" size="16" />
            </Button>
          </Tooltip>
        </div>
      </template>
      <Table :columns="columns" :data="log">
        <template v-slot:page="{ row }">
          {{ get(row, 'meta.url') }}
        </template>
        <template v-slot:type="{ row }">
          <Tag v-if="row.type === 'info'" color="blue">info</Tag>
          <Tag v-if="row.type === 'success'" color="green">success</Tag>
          <Tag v-if="row.type === 'warning'" color="orange">warning</Tag>
          <Tag v-if="row.type === 'error'" color="red">error</Tag>
        </template>
        <template v-slot:more="{ row }">
          <Button type="primary" @click="handleMore(row)">查看</Button>
        </template>
      </Table>
    </Card>
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import { get } from 'lodash';

export default {
  name: 'SystemLog',
  data() {
    return {
      columns: [
        {
          title: '时间',
          key: 'time',
          width: 180,
        },
        {
          title: '信息',
          key: 'message',
          minWidth: 300,
        },
        {
          title: '触发页面',
          slot: 'page',
          minWidth: 300,
        },
        {
          title: '类型',
          width: 100,
          slot: 'type',
        },
        {
          title: '详细信息',
          width: 100,
          slot: 'more',
        },
      ],
    };
  },
  computed: {
    ...mapState('admin/log', ['log']),
  },
  methods: {
    ...mapMutations('admin/log', ['clean']),
    get,
    handleMore(log) {
      this.$Notice.info({
        title: '提示',
        desc: '请在浏览器控制台查看完整日志',
      });
      this.$log.capsule('iView Admin', '完整日志内容', 'primary');
      console.group('完整日志');
      console.log('message ', log.message);
      console.log('time: ', log.time);
      console.log('type: ', log.type);
      console.log('meta: ', log.meta);
      console.groupEnd();
    },
  },
};
</script>
