<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.规则名称.label" :prop="formItemsConfig.规则名称.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.规则名称.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.规则名称.placeholder"
                    :maxlength="formItemsConfig.规则名称.maxlength"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.匹配规则.label" :prop="formItemsConfig.匹配规则.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.匹配规则.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.匹配规则.placeholder"
                    :maxlength="formItemsConfig.匹配规则.maxlength"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.替换值.label" :prop="formItemsConfig.替换值.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.替换值.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.替换值.placeholder"
                    :maxlength="formItemsConfig.替换值.maxlength"
                />
              </FormItem>
            </Col>
          </Row>

          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.优先级.label" :prop="formItemsConfig.优先级.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.优先级.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.优先级.placeholder"
                    :maxlength="formItemsConfig.优先级.maxlength"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';

import mixinsPageForm from '@/mixins/mixinsPageForm';
import { getmgwordDetail, addmgwordItem, editmgwordItem } from '@/api/bigModel/mgword';
import { isNonNullNumber } from '@/libs/lan';
import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG, RANGE_MAX_FLOAT, RANGE_MIN_FLOAT, MSG_RANGE_INTEGER, MSG_RANGE_FLOAT } from '@/define';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getmgwordDetail,
        },
        addDetailData: {
          apiFun: addmgwordItem,
          successMsg: '保存成功',
        },
        editDetailData: {
          apiFun: editmgwordItem,
          successMsg: '修改成功',
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'mgword-list',
        },
      },
      // 页面配置-表单
      pageItemsConfig: {
        // --- 基础信息 ---
        规则名称: {
          editFormItem: {
            valuekey: 'ruleName',
            formRules: [{ ruleType: 'required' }],
          },
        },
        匹配规则: {
          editFormItem: {
            valuekey: 'matchPattern',
            formRules: [{ ruleType: 'required' }],
          },
        },
        替换值: {
          editFormItem: {
            valuekey: 'replaceValue',
            formRules: [{ ruleType: 'required' }],
          },
        },
        优先级: {
          editFormItem: {
            valuekey: 'priority',
          },
        },
      },
    };
  },
  created() {
    this.initDetailPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
