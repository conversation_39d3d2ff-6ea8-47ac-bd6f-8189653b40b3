<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
          ref="searchForm"
          :searchFormInitData="searchFormInitData"
          :searchFormListOrder="searchFormListOrder"
          :searchFormItems="searchFormItems"
          @on-search="handleSearch"
          @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div v-if="pagePermission.addListItems || pagePermission.delListItems" class="content-btn">
          <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">添加</Button>
          <Button v-if="pagePermission.delListItems" type="error" ghost @click="delListItems()">删除</Button>
        </div>
        <div class="content-table">
          <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
            <template v-slot:action="{ row }">
              <span v-if="pagePermission.showListItem" class="action-btn" @click="showListItem({ record: row })">查看详情</span>
              <span v-if="pagePermission.editListItem" class="action-btn" @click="editListItem({ record: row })">修改</span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
            :total="listConfig.total"
            :model-value="listConfig.page"
            :page-size="listConfig.size"
            show-sizer
            transfer
            show-elevator
            show-total
            @on-page-size-change="changeSize"
            @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getmgwordList, deletemgwordItems } from '@/api/bigModel/mgword';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getmgwordList,
        },
        delListItems: {
          apiFun: deletemgwordItems,
          confirm: {
            content: '是否确认删除？',
          },
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'mgword-list-detail',
          permCode: 'ivsmp.big-model.safety-fence.mgcgl.detail',
        },
        addListItems: {
          routerName: 'mgword-list-add',
          permCode: 'ivsmp.big-model.safety-fence.mgcgl.add',
        },
        editListItem: {
          routerName: 'mgword-list-edit',
          permCode: 'ivsmp.big-model.safety-fence.mgcgl.edit',
        },
        delListItems: {
          permCode: 'ivsmp.big-model.safety-fence.mgcgl.delete',
        },
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['规则名称','匹配规则'],
      pageItemsConfig: {
        规则名称: {
          searchFormItem: {
            valuekey: 'ruleName',
          },
          listColumn: {
            valuekey: 'ruleName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        匹配规则: {
          searchFormItem: {
            valuekey: 'matchPattern',
          },
          listColumn: {
            valuekey: 'matchPattern',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        替换值: {
          listColumn: {
            valuekey: 'replaceValue',
            minWidth: 80,
          },
        },
        优先级: {
          listColumn: {
            valuekey: 'priority',
            minWidth: 80,
          },
        },
        创建人: {
          listColumn: {
            valuekey: 'createByName',
            width: 100,
            renderPrecision: 2,
          },
        },
        创建时间: {
          listColumn: {
            valuekey: 'createTime',
            width: 170,
            renderType: 'YMDHMS'
          },
        }
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['规则名称', '匹配规则', '替换值', '优先级', '创建人', '创建时间'];
      if (this.pagePermission.delListItems) {
        columns.unshift('CHECKBOX');
      }
      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
