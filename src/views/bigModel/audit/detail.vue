<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.原始内容.label" :prop="formItemsConfig.原始内容.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.原始内容.prop]"
                    disabled="disabled"
                    :placeholder="formItemsConfig.原始内容.placeholder"
                    :maxlength="formItemsConfig.原始内容.maxlength"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.脱敏后的内容.label" :prop="formItemsConfig.脱敏后的内容.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.脱敏后的内容.prop]"
                    disabled="disabled"
                    :placeholder="formItemsConfig.脱敏后的内容.placeholder"
                    :maxlength="formItemsConfig.脱敏后的内容.maxlength"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.审查状态.label" :prop="formItemsConfig.审查状态.prop">
                <Input v-if="detailFormConfig.isReadonly &&  (detailData[formItemsConfig.审查状态.prop] =='' || detailData[formItemsConfig.审查状态.prop]==null)" v-model="msg" disabled="disabled"/>
                <SelectDic v-else
                    v-model:modelValue="detailData[formItemsConfig.审查状态.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.审查状态.placeholder"
                    :dicType="formItemsConfig.审查状态.dicType"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.审查后内容.label" :prop="formItemsConfig.审查后内容.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.审查后内容.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.审查后内容.placeholder"
                    :maxlength="formItemsConfig.审查后内容.maxlength"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.审查意见.label" :prop="formItemsConfig.审查意见.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.审查意见.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.审查意见.placeholder"
                    :maxlength="formItemsConfig.审查意见.maxlength"
                />
              </FormItem>
            </Col>
          </Row>




        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';

import mixinsPageForm from '@/mixins/mixinsPageForm';
import { getauditDetail, addauditItem, editauditItem } from '@/api/bigModel/audit';
import { isNonNullNumber } from '@/libs/lan';
import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG, RANGE_MAX_FLOAT, RANGE_MIN_FLOAT, MSG_RANGE_INTEGER, MSG_RANGE_FLOAT } from '@/define';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      msg:"未审查",
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getauditDetail,
        },
        addDetailData: {
          apiFun: addauditItem,
          successMsg: '保存成功',
        },
        editDetailData: {
          apiFun: editauditItem,
          successMsg: '修改成功',
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'bigmodel-audit-list',
        },
      },
      // 页面配置-表单
      pageItemsConfig: {
        // --- 基础信息 ---
        原始内容: {
          editFormItem: {
            valuekey: 'originalContent',
            formRules: [{ ruleType: 'required' }],
          },
        },
        脱敏后的内容: {
          editFormItem: {
            valuekey: 'tmReviewedContent',
            formRules: [{ ruleType: 'required' }],
          },
        },
        审查状态: {
          editFormItem: {
            valuekey: 'reviewStatus',
            formRules: [{ ruleType: 'required' }],
            dicType: 3003,
          },
        },
        审查后内容: {
          editFormItem: {
            valuekey: 'reviewedContent',
            formRules: [{ ruleType: 'required' }],
          },
        },
        审查意见: {
          editFormItem: {
            valuekey: 'reviewOpinion',
            formRules: [{ ruleType: 'required' }],
          },
        }

      },
    };
  },
  created() {
    this.initDetailPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
