<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
          ref="searchForm"
          :searchFormInitData="searchFormInitData"
          :searchFormListOrder="searchFormListOrder"
          :searchFormItems="searchFormItems"
          @on-search="handleSearch"
          @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div v-if="pagePermission.addListItems || pagePermission.delListItems" class="content-btn">
          <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">添加</Button>
          <Button v-if="pagePermission.delListItems" type="error" ghost @click="delListItems()">删除</Button>
        </div>
        <div class="content-table">
          <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
            <template v-slot:action="{ row }">
              <span v-if="pagePermission.showListItem" class="action-btn" @click="showListItem({ record: row })">查看详情</span>
              <span v-if="pagePermission.editListItem" class="action-btn" @click="editData(row)">审查</span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
            :total="listConfig.total"
            :model-value="listConfig.page"
            :page-size="listConfig.size"
            show-sizer
            transfer
            show-elevator
            show-total
            @on-page-size-change="changeSize"
            @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getauditList, deleteauditItems } from '@/api/bigModel/audit';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getauditList,
        },
        delListItems: {
          apiFun: deleteauditItems,
          confirm: {
            content: '是否确认删除？',
          },
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'bigmodel-audit-list-detail',
          permCode: 'ivsmp.big-model.safety-fence.audit.detail',
        },
        addListItems: {
          routerName: 'bigmodel-audit-list-add',
          permCode: 'ivsmp.big-model.safety-fence.audit.add',
        },
        editListItem: {
          routerName: 'bigmodel-audit-list-edit',
          permCode: 'ivsmp.big-model.safety-fence.audit.edit',
        },
        delListItems: {
          permCode: 'ivsmp.big-model.safety-fence.audit.delete',
        },
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['原始内容','脱敏后的内容','审查后内容'],
      pageItemsConfig: {
        原始内容: {
          searchFormItem: {
            valuekey: 'originalContent',
          },
          listColumn: {
            valuekey: 'originalContent',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        脱敏后的内容: {
          searchFormItem: {
            valuekey: 'tmReviewedContent',
          },
          listColumn: {
            valuekey: 'tmReviewedContent',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        审查人: {
          listColumn: {
            valuekey: 'reviewerUserName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        审查状态: {
          listColumn: {
            valuekey: 'reviewStatus',
            minWidth: 80,
            render: (h, params) => {
               if(params.row.reviewStatus=='300302'){
                return h('div',"不通过");
              }else if(params.row.reviewStatus=='300301'){
                return h('div',"通过");
              }else {
                return h('div',"未审查");
              }

            },
          },
        },
        审查后内容: {
          searchFormItem: {
            valuekey: 'reviewedContent',
          },
          listColumn: {
            valuekey: 'reviewedContent',
            minWidth: 80,
            renderType: 'ellipsis',

          },
        },
        审查意见: {
          listColumn: {
            valuekey: 'reviewOpinion',
            width: 100,
            renderPrecision: 2,
          },
        },
        审查完成时间: {
          listColumn: {
            valuekey: 'reviewTime',
            width: 170,
            renderType: 'YMDHMS'
          },
        },
        创建时间: {
          listColumn: {
            valuekey: 'createTime',
            width: 170,
            renderType: 'YMDHMS'
          },
        }
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['原始内容','脱敏后的内容', '审查人', '审查状态','审查后内容', '审查意见', '审查完成时间', '创建时间'];
      if (this.pagePermission.delListItems) {
        columns.unshift('CHECKBOX');
      }
      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
  },
  methods: {
    editData(row){
      if(row.reviewStatus){
        this.$Message.success("已审查，请勿重复操作");
        return

      }
      this.editListItem({ "record": row })

    }
  }
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
