<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
          ref="searchForm"
          :searchFormInitData="searchFormInitData"
          :searchFormListOrder="searchFormListOrder"
          :searchFormItems="searchFormItems"
          @on-search="handleSearch"
          @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div v-if="pagePermission.addListItems || pagePermission.delListItems" class="content-btn">
          <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">添加</Button>
          <Button v-if="pagePermission.delListItems" type="error" ghost @click="delListItems()">删除</Button>
        </div>
        <div class="content-table">
          <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
            <template v-slot:action="{ row }">
              <span v-if="pagePermission.showListItem" class="action-btn" @click="showListItem({ record: row })">查看详情</span>
              <span v-if="pagePermission.editListItem" class="action-btn" @click="editListItem({ record: row })">修改</span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
            :total="listConfig.total"
            :model-value="listConfig.page"
            :page-size="listConfig.size"
            show-sizer
            transfer
            show-elevator
            show-total
            @on-page-size-change="changeSize"
            @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getwarnList, deletewarnItems } from '@/api/bigModel/warn';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getwarnList,
        },
        delListItems: {
          apiFun: deletewarnItems,
          confirm: {
            content: '是否确认删除？',
          },
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'bigmodel-warn-list-detail',
          permCode: 'ivsmp.big-model.safety-fence.warn.detail',
        },
        addListItems: {
          routerName: 'bigmodel-warn-list-add',
          permCode: 'ivsmp.big-model.safety-fence.warn.add',
        },
        editListItem: {
          routerName: 'bigmodel-warn-list-edit',
          permCode: 'ivsmp.big-model.safety-fence.warn.edit',
        },
        delListItems: {
          permCode: 'ivsmp.big-model.safety-fence.warn.delete',
        },
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['违规内容','关键词'],
      pageItemsConfig: {
        违规内容: {
          searchFormItem: {
            valuekey: 'originalContent',
          },
          listColumn: {
            valuekey: 'originalContent',
            minWidth: 200,
            renderType: 'ellipsis',
          },
        },
        关键词: {
          searchFormItem: {
            valuekey: 'alertTriggerKeywords',
          },
          listColumn: {
            valuekey: 'alertTriggerKeywords',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        创建时间: {
          listColumn: {
            valuekey: 'createTime',
            width: 170,
            renderType: 'YMDHMS'
          },
        }
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['违规内容', '关键词', '创建时间'];
      if (this.pagePermission.delListItems) {
        columns.unshift('CHECKBOX');
      }
      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
