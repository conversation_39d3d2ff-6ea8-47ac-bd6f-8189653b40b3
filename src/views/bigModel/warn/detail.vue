<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon :label-width="120">
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.违规内容.label" :prop="formItemsConfig.违规内容.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.违规内容.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.违规内容.placeholder"
                    :maxlength="formItemsConfig.违规内容.maxlength"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.关键词.label" :prop="formItemsConfig.关键词.prop">
                <Input
                    v-model.trim="detailData[formItemsConfig.关键词.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.关键词.placeholder"
                    :maxlength="formItemsConfig.关键词.maxlength"
                />
              </FormItem>
            </Col>
          </Row>

        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';

import mixinsPageForm from '@/mixins/mixinsPageForm';
import { getwarnDetail, addwarnItem, editwarnItem } from '@/api/bigModel/warn';
import { isNonNullNumber } from '@/libs/lan';
import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG, RANGE_MAX_FLOAT, RANGE_MIN_FLOAT, MSG_RANGE_INTEGER, MSG_RANGE_FLOAT } from '@/define';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getwarnDetail,
        },
        addDetailData: {
          apiFun: addwarnItem,
          successMsg: '保存成功',
        },
        editDetailData: {
          apiFun: editwarnItem,
          successMsg: '修改成功',
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'bigmodel-warn-list',
        },
      },
      // 页面配置-表单
      pageItemsConfig: {
        // --- 基础信息 ---
        违规内容: {
          editFormItem: {
            valuekey: 'originalContent',
            formRules: [{ ruleType: 'required' }],
          },
        },
        关键词: {
          editFormItem: {
            valuekey: 'alertTriggerKeywords',
            formRules: [{ ruleType: 'required' }],
          },
        },
      },
    };
  },
  created() {
    this.initDetailPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
