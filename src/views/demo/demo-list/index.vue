<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
        ref="searchForm"
        :searchFormInitData="searchFormInitData"
        :searchFormListOrder="searchFormListOrder"
        :searchFormItems="searchFormItems"
        @on-search="handleSearch"
        @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div v-if="pagePermission.addListItems || pagePermission.delListItems" class="content-btn">
          <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">添加</Button>
          <Button v-if="pagePermission.delListItems" type="error" ghost @click="delListItems()">删除</Button>
        </div>
        <div class="content-table">
          <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
            <template v-slot:action="{ row }">
              <span v-if="pagePermission.showListItem" class="action-btn" @click="showListItem({ record: row })">查看详情</span>
              <span v-if="pagePermission.editListItem" class="action-btn" @click="editListItem({ record: row })">修改</span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
          :total="listConfig.total"
          :model-value="listConfig.page"
          :page-size="listConfig.size"
          show-sizer
          transfer
          show-elevator
          show-total
          @on-page-size-change="changeSize"
          @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getDemoList, deleteDemoItems } from '@/api/demo';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getDemoList,
        },
        delListItems: {
          apiFun: deleteDemoItems,
          confirm: {
            content: '是否确认删除套餐？',
            comments: '删除后不可恢复，如需再次使用该套餐，需重新添加！',
          },
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'demo-list-detail',
          permCode: 'monitor.demo.demo-list.detail',
        },
        addListItems: {
          routerName: 'demo-list-add',
          permCode: 'monitor.demo.demo-list.add',
        },
        editListItem: {
          routerName: 'demo-list-edit',
          permCode: 'monitor.demo.demo-list.edit',
        },
        delListItems: {
          permCode: 'monitor.demo.demo-list.delete',
        },
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['套餐名称', '适用客户', '套餐类型', '流量类型'],
      pageItemsConfig: {
        套餐名称: {
          searchFormItem: {
            valuekey: 'packageName',
          },
          listColumn: {
            valuekey: 'packageName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        适用客户: {
          searchFormItem: {
            valuekey: 'customName',
          },
          listColumn: {
            valuekey: 'carCompany',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        套餐类型: {
          searchFormItem: {
            valuekey: 'packageType',
            formItemType: 'Select',
            dicType: 1301,
          },
          listColumn: {
            valuekey: 'packageTypeName',
            minWidth: 80,
          },
        },
        流量类型: {
          searchFormItem: {
            valuekey: 'flowType',
            formItemType: 'Select',
            dicType: 1307,
          },
          listColumn: {
            valuekey: 'flowTypeName',
            minWidth: 80,
          },
        },
        售价: {
          listColumn: {
            label: '售价 (元)',
            valuekey: 'price',
            width: 100,
            renderType: 'number',
            renderPrecision: 2,
          },
        },
        优惠价: {
          listColumn: {
            label: '优惠价 (元)',
            valuekey: 'bestPrice',
            width: 120,
            renderType: 'number',
            renderPrecision: 2,
          },
        },
        创建时间: {
          listColumn: {
            valuekey: 'createTime',
            width: 170,
          },
        },
        最近修改时间: {
          listColumn: {
            valuekey: 'updateTime',
            width: 170,
          },
        },
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['套餐名称', '适用客户', '套餐类型', '流量类型', '售价', '优惠价', '流量大小', '创建时间', '最近修改时间'];
      if (this.pagePermission.delListItems) {
        columns.unshift('CHECKBOX');
      }
      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
