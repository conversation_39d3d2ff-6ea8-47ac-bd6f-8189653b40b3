<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 列表 -->
      <div class="content">
        <div class="content-table">
          <Table
            ref="listTable"
            :columns="listColumns"
            :data="listData"
            :loading="listLoading"
            stripe
          >
            <template v-slot:action="{ row }">
              <span
                v-if="pagePermission.showListItem"
                class="action-btn"
                @click="showListItem({ record: row, isReadonly: false })"
              >
                详情
              </span>
              <span
                v-if="pagePermission.editListItem"
                class="action-btn"
                @click="editListItem({ record: row })"
              >
                修改
              </span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
          :total="listConfig.total"
          :model-value="listConfig.page"
          :page-size="listConfig.size"
          show-sizer
          transfer
          show-elevator
          show-total
          @on-page-size-change="changeSize"
          @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import mixinsPageList from '@/mixins/mixinsPageList';
import { getTableList } from '@/api/safety/emergency-workflow';

const orderTypeList = [
  {
    value: '1',
    label: '攻击源分析',
  },
  {
    value: '2',
    label: '多车关联分析',
  },
  {
    value: '3',
    label: '复杂攻击分析',
  },
  {
    value: '4',
    label: '外部情报源',
  },
  {
    value: '5',
    label: '手工录入',
  },
];

export default {
  mixins: [mixinsPageList],
  components: {},
  data() {
    return {
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getTableList,
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'emergency-workflow-edit',
          permCode: 'ivsmp.waemergency-workflow.detail',
        },
        editListItem: {
          routerName: 'emergency-workflow-edit',
          permCode: 'ivsmp.waemergency-workflow.edit',
        },
      },
      // 页面配置-列表、搜索
      pageItemsConfig: {
        配置id: {
          listColumn: {
            valuekey: 'id',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        配置类型: {
          listColumn: {
            valuekey: 'orderType',
            minWidth: 120,
            // renderType: 'ellipsis',
            render: (h, params) =>
              orderTypeList.filter((k) => k.value == params.row.orderType)[0]?.label,
          },
        },
        创建时间: {
          listColumn: {
            valuekey: 'createdAt',
            minWidth: 120,
            renderType: 'YMDHMS',
          },
        },
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['配置id', '配置类型', '创建时间'];

      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {},
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="less" scoped>
@import "~@/styles/list-search-page.less";
</style>
