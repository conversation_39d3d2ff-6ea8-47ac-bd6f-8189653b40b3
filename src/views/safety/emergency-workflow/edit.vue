<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form
        v-if="!isLoadingGetDetail"
        ref="detailForm"
        :model="detailData"
        :rules="detailFormRules"
        label-colon
        :label-width="120"
      >
        <Card dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.配置类型.label"
                :prop="formItemsConfig.配置类型.prop"
              >
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.配置类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.配置类型.placeholder"
                  :list="formItemsConfig.配置类型.list"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>

        <Card title="流程节点处理人" dis-hover :bordered="false">
          <Row>
            <Col span="24">
              <FormItem
                :label="formItemsConfig.告警研判.label"
                :prop="formItemsConfig.告警研判.prop"
              >
                <div style="display: flex; align-item: center">
                  <SelectDic
                    style="flex: 1; margin-right: 10px"
                    v-model:modelValue="detailData[formItemsConfig.告警研判.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.告警研判.placeholder"
                    :list="formItemsConfig.告警研判.list"
                  />
                  <Input
                    style="flex: 1"
                    v-model:modelValue="detailData[formItemsConfig.告警研判对象.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.告警研判对象.placeholder"
                  />
                </div>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem
                :label="formItemsConfig.告警派发.label"
                :prop="formItemsConfig.告警派发.prop"
              >
                <div style="display: flex; align-item: center">
                  <SelectDic
                    style="flex: 1; margin-right: 10px"
                    v-model:modelValue="detailData[formItemsConfig.告警派发.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.告警派发.placeholder"
                    :list="formItemsConfig.告警派发.list"
                  />
                  <Input
                    style="flex: 1"
                    v-model:modelValue="detailData[formItemsConfig.告警派发对象.prop]"
                    :disabled="detailFormConfig.isReadonly"
                    :placeholder="formItemsConfig.告警派发对象.placeholder"
                  />
                </div>
              </FormItem>
            </Col>
          </Row>
        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">
            保存
          </Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';

import { getDetail, updateNode } from '@/api/safety/emergency-workflow';

import mixinsPageForm from '@/mixins/mixinsPageForm';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getDetail,
        },
        editDetailData: {
          apiFun: updateNode,
          successMsg: '修改成功',
        },
        addDetailData: {
          apiFun: updateNode,
          successMsg: '修改成功',
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'emergency-workflow-list',
        },
      },
      // 页面配置-表单
      pageItemsConfig: {
        // --- 基础信息 ---
        配置类型: {
          editFormItem: {
            valuekey: 'orderType',
            list: [
              {
                value: '1',
                label: '攻击源分析',
              },
              {
                value: '2',
                label: '多车关联分析',
              },
              {
                value: '3',
                label: '复杂攻击分析',
              },
              {
                value: '4',
                label: '外部情报源',
              },
              {
                value: '5',
                label: '手工录入',
              },
            ],
          },
        },
        告警研判: {
          editFormItem: {
            valuekey: 'judgeNodeType',
            list: [
              {
                value: '1',
                label: '角色',
              },
              {
                value: '2',
                label: '人员',
              },
            ],
          },
        },
        告警派发: {
          editFormItem: {
            valuekey: 'dispatchNodeType',
            list: [
              {
                value: '1',
                label: '角色',
              },
              {
                value: '2',
                label: '人员',
              },
            ],
          },
        },
        告警研判对象: {
          editFormItem: {
            valuekey: 'judgeNodeTargets',
          },
        },
        告警派发对象: {
          editFormItem: {
            valuekey: 'dispatchNodeTargets',
          },
        },
      },
    };
  },
  created() {
    this.initDetailPage();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
