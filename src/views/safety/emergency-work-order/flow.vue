<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 列表 -->
      <div class="content">
        <div class="content-table">
          <Table
            ref="listTable"
            :columns="listColumns"
            :data="listData"
            :loading="listLoading"
            stripe
          ></Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
          :total="listConfig.total"
          :model-value="listConfig.page"
          :page-size="listConfig.size"
          show-sizer
          transfer
          show-elevator
          show-total
          @on-page-size-change="changeSize"
          @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import mixinsPageList from '@/mixins/mixinsPageList';
import { getFlowList } from '@/api/safety/emergency-work-order';

export default {
  mixins: [mixinsPageList],
  components: {},
  data() {
    return {
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getFlowList,
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'audit-detail',
          permCode: 'ivsmp.audit.audit-detail',
        },
      },
      // 页面配置-列表、搜索
      pageItemsConfig: {
        流程id: {
          listColumn: {
            valuekey: 'alarmId',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        节点名称: {
          listColumn: {
            valuekey: 'nodeName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        处理人: {
          listColumn: {
            valuekey: 'processorId',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        处理意见: {
          listColumn: {
            valuekey: 'comments',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        处理时间: {
          listColumn: {
            valuekey: 'processTime',
            minWidth: 120,
            renderType: 'YMDHMS',
          },
        },
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['流程id', '节点名称', '处理人', '处理意见', '处理时间'];
      return columns;
    },
  },
  created() {},
  mounted() {
    const replaceRequestData = (requestData) => ({
      ...requestData,
      ...this.$route.params,
    });
    this.pageConfigAPI.getList.replaceRequestData = replaceRequestData;
    this.initPage();
  },
  methods: {
    relationsLog() {},
  },
};
</script>

<style lang="less" scoped>
@import "~@/styles/list-search-page.less";
</style>
