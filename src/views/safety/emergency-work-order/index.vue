<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
        ref="searchForm"
        :searchFormInitData="searchFormInitData"
        :searchFormListOrder="searchFormListOrder"
        :searchFormItems="searchFormItems"
        @on-search="handleSearch"
        @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div class="content-table">
          <Table
            ref="listTable"
            :columns="listColumns"
            :data="listData"
            :loading="listLoading"
            stripe
          >
            <template v-slot:action="{ row }">
              <span
                v-if="pagePermission.showListItem"
                class="action-btn"
                @click="showListItem({ record: row, params: { ...row } })"
              >
                详情
              </span>
              <span
                v-if="pagePermission.processInfo"
                class="action-btn"
                @click="toFlow({ record: row })"
              >
                流程信息
              </span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
          :total="listConfig.total"
          :model-value="listConfig.page"
          :page-size="listConfig.size"
          show-sizer
          transfer
          show-elevator
          show-total
          @on-page-size-change="changeSize"
          @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getTableList } from '@/api/safety/emergency-work-order';

const alarmLevelList = [
  {
    value: '1',
    label: '高',
  },
  {
    value: '2',
    label: '中',
  },
  {
    value: '3',
    label: '低',
  },
];
const sourceDetailList = [
  {
    value: '1',
    label: '攻击源分析',
  },
  {
    value: '2',
    label: '多车关联分析',
  },
  {
    value: '3',
    label: '复杂攻击分析',
  },
  {
    value: '4',
    label: '外部情报源',
  },
  {
    value: '5',
    label: '手工录入',
  },
];

const statusList = [
  {
    value: '1',
    label: '待研判',
  },
  {
    value: '2',
    label: '待派发',
  },
  {
    value: '3',
    label: '完成',
  },
  {
    value: '4',
    label: '不处置',
  },
];

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getTableList,
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'emergency-work-order-detail',
          permCode: 'ivsmp.waemergency-work-order.detail',
        },
        processInfo: {
          routerName: 'emergency-work-order-flow',
          permCode: 'ivsmp.waemergency-work-order.processinfo',
        },
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['告警类型', '告警名称', '告警等级', '处置状态'],
      pageItemsConfig: {
        工单id: {
          listColumn: {
            valuekey: 'id',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        告警id: {
          listColumn: {
            valuekey: 'alarmId',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        告警名称: {
          searchFormItem: {
            valuekey: 'alarmName',
          },
          listColumn: {
            valuekey: 'alarmName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        告警类型: {
          searchFormItem: {
            valuekey: 'sourceDetail',
            formItemType: 'Select',
            optionList: sourceDetailList,
          },
          listColumn: {
            valuekey: 'sourceDetail',
            minWidth: 120,
            // renderType: 'ellipsis',
            render: (h, params) =>
              sourceDetailList.filter((k) => k.value == params.row.sourceDetail)[0]?.label,
          },
        },
        告警等级: {
          searchFormItem: {
            valuekey: 'alarmLevel',
            formItemType: 'Select',
            optionList: alarmLevelList,
          },
          listColumn: {
            valuekey: 'alarmLevel',
            minWidth: 120,
            // renderType: 'ellipsis',
            render: (h, params) =>
              alarmLevelList.filter((k) => k.value == params.row.alarmLevel)[0]?.label,
          },
        },
        处置人: {
          listColumn: {
            valuekey: 'processorId',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        处置状态: {
          searchFormItem: {
            valuekey: 'status',
            formItemType: 'Select',
            optionList: statusList,
          },
          listColumn: {
            valuekey: 'status',
            minWidth: 120,
            // renderType: 'ellipsis',
            render: (h, params) => statusList.filter((k) => k.value == params.row.status)[0]?.label,
          },
        },
        处置时间: {
          listColumn: {
            valuekey: 'processTime',
            minWidth: 120,
            renderType: 'YMDHMS',
          },
        },
        处置结果: {
          searchFormItem: {
            valuekey: 'comments',
            formItemType: 'Select',
            dicType: 1307,
          },
          listColumn: {
            label: '处置意见',
            valuekey: 'comments',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = [
        '工单id',
        '告警id',
        '告警名称',
        '告警类型',
        '告警时间',
        '告警等级',
        '处置人',
        '处置状态',
        '处置时间',
        '处置结果',
      ];
      if (this.pagePermission.processInfo || this.pagePermission.showListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
  },
  methods: {
    toFlow({ record }) {
      this.$router.push({
        name: 'emergency-work-order-flow',
        params: {
          record: JSON.stringify({
            orderId: record.id,
          }),
          orderId: record.id,
          isReadonly: true,
          toChildrenPage: true,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
