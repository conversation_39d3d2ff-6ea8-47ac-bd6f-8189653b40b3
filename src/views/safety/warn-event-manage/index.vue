<template>
  <div class="page-wrap">
    <div class="base-table">
      <Tabs type="card">
        <TabPane label="攻击源告警">
          <gjy @relations="relations" @deal="deal" @addItem="addItem" />
        </TabPane>
        <TabPane label="多车关联告警">
          <dcgl @relations="relations" @deal="deal" @addItem="addItem" />
        </TabPane>
        <TabPane label="复杂攻击告警">
          <fcgj @relations="relations" @deal="deal" @addItem="addItem" />
        </TabPane>
      </Tabs>
    </div>

    <Modal v-model="modal" title="处置" footer-hide>
      <div style="display: flex; align-item: center; justify-content: center">
        <Button type="default" @click="modal = false">取消</Button>
        <Button type="primary" style="margin-left: 10px; margin-right: 10px" @click="dealItem('2')">
          忽略
        </Button>
        <Button type="success" @click="dealItem('1')">派单</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import gjy from './gjy.vue';
import dcgl from './dcgl.vue';
import fcgj from './fcgj.vue';

import { dealApi } from '@/api/safety/warn-event-manage';

export default {
  mixins: [],
  components: {
    gjy,
    dcgl,
    fcgj,
  },
  data() {
    return {
      curDealItem: null,
      modal: false,
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    relations(row) {
      this.$router.push({
        name: 'warn-event-manage-relations',
        params: {
          alarmId: row.id,
        },
      });
    },
    deal(row) {
      this.modal = true;
      this.curDealItem = row;
    },
    dealItem(type) {
      dealApi({
        id: this.curDealItem.id,
        evaluationResult: type,
      }).then((res) => {
        this.modal = false;
        this.curDealItem = null;
      });
    },
    addItem(type) {
      this.$router.push({
        name: 'warn-event-manage-add',
        params: {
          sourceDetail: type,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
