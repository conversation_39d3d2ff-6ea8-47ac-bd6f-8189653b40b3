const sourceTypeList = [
    {
        value: 1,
        label: '内部规则',
    },
    {
        value: 2,
        label: '外部情报源',
    },
    {
        value: 3,
        label: '手工录入',
    },
];
const targetTypeList = [
    {
        value: 1,
        label: '品牌',
    },
    {
        value: 2,
        label: '车型',
    },
    {
        value: 3,
        label: '车辆',
    },
];

const needEvaluationList = [
    {
        value: 1,
        label: '是',
    },
    {
        value: 2,
        label: '否',
    },
];

const evaluationResultList = [
    {
        value: 1,
        label: '生成工单',
    },
    {
        value: 2,
        label: '忽略',
    },
];

const alarmLevelList = [
    {
        value: 1,
        label: '高',
    },
    {
        value: 2,
        label: '中',
    },
    {
        value: 3,
        label: '低',
    },
];

const defaultConfig = {
    来源类型: {
        searchFormItem: {
            valuekey: 'sourceType',
            formItemType: 'Select',
            optionList: sourceTypeList,
        },
        listColumn: {
            valuekey: 'sourceType',
            minWidth: 120,
            // renderType: 'ellipsis',
            render: (h, params) => sourceTypeList.filter(k => k.value == params.row.sourceType)[0]?.label
        },
    },
    对象类型: {
        searchFormItem: {
            valuekey: 'targetType',
            formItemType: 'Select',
            optionList: targetTypeList,
        },
        listColumn: {
            valuekey: 'targetType',
            minWidth: 120,
            // renderType: 'ellipsis',
            render: (h, params) => targetTypeList.filter(k => k.value == params.row.targetType)[0]?.label
        },
    },
    是否评估: {
        searchFormItem: {
            valuekey: 'needEvaluation',
            formItemType: 'Select',
            optionList: needEvaluationList,
        },
        listColumn: {
            valuekey: 'needEvaluation',
            minWidth: 120,
            // renderType: 'ellipsis',
            render: (h, params) => needEvaluationList.filter(k => k.value == params.row.needEvaluation)[0]?.label
        },
    },
    处置结果: {
        searchFormItem: {
            valuekey: 'evaluationResult',
            formItemType: 'Select',
            optionList: evaluationResultList
        },
        listColumn: {
            valuekey: 'evaluationResult',
            minWidth: 120,
            // renderType: 'ellipsis',
            render: (h, params) => evaluationResultList.filter(k => k.value == params.row.evaluationResult)[0]?.label
        },
    },
    告警id: {
        listColumn: {
            valuekey: 'id',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
    告警名称: {
        listColumn: {
            valuekey: 'alarmName',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
    告警时间: {
        listColumn: {
            valuekey: 'alarmTime',
            minWidth: 120,
            renderType: 'YMDHMS',
        },
    },
    告警等级: {
        listColumn: {
            valuekey: 'alarmLevel',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
    攻击次数: {
        listColumn: {
            valuekey: 'attackCount',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
    处置人: {
        listColumn: {
            valuekey: 'evaluatorId', // `evaluator_id` bigint DEFAULT NULL COMMENT '评估人ID',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
};

const gjy = {
    源IP: {
        searchFormItem: {
            valuekey: 'sourceIp',
        },
        listColumn: {
            valuekey: 'sourceIp',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
    ...defaultConfig
};

const dcgl = {
    事件类型: {
        searchFormItem: {
            valuekey: 'eventTypeId', // 是个下拉框，但不知道从那里获取
        },
        listColumn: {
            valuekey: 'eventTypeId',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
    ...defaultConfig
};

const fcgj = {
    攻击目标: {
        searchFormItem: {
            valuekey: 'mm1',
        },
        listColumn: {
            valuekey: 'mm1',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
    攻击链路: {
        listColumn: {
            valuekey: 'm10',
            minWidth: 120,
            renderType: 'ellipsis',
        },
    },
    ...defaultConfig
};

export const itemConfig = {
    gjy,
    dcgl,
    fcgj
};
