<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 列表 -->
      <div class="content">
        <div class="content-table">
          <Table
            ref="listTable"
            :columns="listColumns"
            :data="listData"
            :loading="listLoading"
            stripe
          >
            <template v-slot:action="{ row }">
              <span
                v-if="pagePermission.showListItem"
                class="action-btn"
                @click="showListItem({ record: row })"
              >
                关联日志
              </span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
          :total="listConfig.total"
          :model-value="listConfig.page"
          :page-size="listConfig.size"
          show-sizer
          transfer
          show-elevator
          show-total
          @on-page-size-change="changeSize"
          @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import mixinsPageList from '@/mixins/mixinsPageList';
import { relationItem } from '@/api/safety/warn-event-manage';

export default {
  mixins: [mixinsPageList],
  components: {},
  data() {
    return {
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: relationItem,
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'audit-detail',
          permCode: 'ivsmp.audit.audit-detail',
        },
      },
      // 页面配置-列表、搜索
      pageItemsConfig: {
        告警id: {
          listColumn: {
            valuekey: 'alarmId',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        告警名称: {
          listColumn: {
            valuekey: 'alarmName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        事件id: {
          listColumn: {
            valuekey: 'eventId',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        车架号: {
          listColumn: {
            valuekey: 'vin',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        车型: {
          listColumn: {
            valuekey: 'modelName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        事件时间: {
          listColumn: {
            valuekey: 'eventTime',
            minWidth: 120,
            renderType: 'YMDHMS',
          },
        },
        事件类型: {
          listColumn: {
            valuekey: 'eventType',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        威胁等级: {
          listColumn: {
            valuekey: 'threatLevel',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        攻击源地址: {
          listColumn: {
            valuekey: 'sourceIp',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = [
        '告警id',
        '告警名称',
        '事件id',
        '车架号',
        '车型',
        '事件时间',
        '事件类型',
        '威胁等级',
        '攻击源地址',
      ];

      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {},
  mounted() {
    const replaceRequestData = (requestData) => ({
      ...requestData,
      ...this.$route.params,
    });
    this.pageConfigAPI.getList.replaceRequestData = replaceRequestData;
    this.initPage();
  },
  methods: {
    relationsLog() {},
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
