<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form
        v-if="!isLoadingGetDetail"
        ref="detailForm"
        :model="detailData"
        :rules="detailFormRules"
        label-colon
        :label-width="120"
      >
        <Card dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.告警名称.label"
                :prop="formItemsConfig.告警名称.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.告警名称.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.告警名称.placeholder"
                  :maxlength="formItemsConfig.告警名称.maxlength"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.告警级别.label"
                :prop="formItemsConfig.告警级别.prop"
              >
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.告警级别.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.告警级别.placeholder"
                  :list="formItemsConfig.告警级别.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.对象类型.label"
                :prop="formItemsConfig.对象类型.prop"
              >
                <SelectDic
                  v-model="detailData[formItemsConfig.对象类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.对象类型.placeholder"
                  :list="formItemsConfig.对象类型.list"
                  @change-data="targetTypeChange"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.对象.label" :prop="formItemsConfig.对象.prop">
                <SelectDic
                  v-model:modelValue="detailData[formItemsConfig.对象.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.对象.placeholder"
                  :multiple="true"
                  :list="targetList"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.上报人.label" :prop="formItemsConfig.上报人.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.上报人.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.上报人.placeholder"
                  :maxlength="formItemsConfig.上报人.maxlength"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.联系方式.label"
                :prop="formItemsConfig.联系方式.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.联系方式.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.联系方式.placeholder"
                  :maxlength="formItemsConfig.联系方式.maxlength"
                />
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem
                :label="formItemsConfig.告警详情.label"
                :prop="formItemsConfig.告警详情.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.告警详情.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :placeholder="formItemsConfig.告警详情.placeholder"
                  type="textarea"
                  :rows="2"
                  :maxlength="formItemsConfig.告警详情.maxlength"
                  show-word-limit
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">
            保存
          </Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';

import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';
import { add } from '@/api/safety/warn-event-manage';

import mixinsPageForm from '@/mixins/mixinsPageForm';
import { getDetail } from '@/api/audit';
import { isNonNullNumber } from '@/libs/lan';
import {
  LENGTH_MAX_INPUT,
  LENGTH_MAX_INPUT_LONG,
  RANGE_MAX_FLOAT,
  RANGE_MIN_FLOAT,
  MSG_RANGE_INTEGER,
  MSG_RANGE_FLOAT,
} from '@/define';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getDetail,
        },
        addDetailData: {
          apiFun: add,
          successMsg: '添加成功',
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'warn-event-manage-list',
        },
      },

      brandList: [],
      modelList: [],
      carList: [],
      targetList: [],
    };
  },
  computed: {
    // 页面配置-表单
    pageItemsConfig() {
      return {
        // --- 基础信息 ---
        告警名称: {
          editFormItem: {
            valuekey: 'alarmName',
          },
        },
        告警级别: {
          editFormItem: {
            valuekey: 'alarmLevel',
            list: [
              {
                value: '1',
                label: '高',
              },
              {
                value: '2',
                label: '中',
              },
              {
                value: '3',
                label: '低',
              },
            ],
          },
        },
        对象类型: {
          editFormItem: {
            valuekey: 'targetType',
            list: [
              {
                value: '1',
                label: '品牌',
              },
              {
                value: '2',
                label: '车型',
              },
              {
                value: '3',
                label: '车辆',
              },
            ],
          },
        },
        对象: {
          editFormItem: {
            valuekey: 'targetIds',
            list: [],
            // optionList: this.targetList,
          },
        },
        上报人: {
          editFormItem: {
            valuekey: 'reporter',
          },
        },
        联系方式: {
          editFormItem: {
            valuekey: 'reporterContact',
            formRules: [{ ruleType: 'phone' }],
          },
        },
        告警详情: {
          editFormItem: {
            valuekey: 'manualAlarmInfo',
            formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }],
          },
        },
      };
    },
  },
  async mounted() {
    await this.getBrand();
    this.initDetailPage();

    const replactRequestData = ({ requestData }) => ({
      ...requestData,
      sourceDetail: this.$route.params.sourceDetail,
      targetIds: requestData.targetIds.join(','),
    });
    this.pageConfigAPI.addDetailData.replactRequestData = replactRequestData;
  },
  methods: {
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach((item) => {
          item.value = item.id;
          item.label = item.name;
        });
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      }
    },

    targetTypeChange(val) {
      if (val == '1') {
        this.targetList = this.brandList;
      }
      if (val == '2') {
        this.targetList = this.modelList;
      }
      if (val == '3') {
        this.targetList = this.carList;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
