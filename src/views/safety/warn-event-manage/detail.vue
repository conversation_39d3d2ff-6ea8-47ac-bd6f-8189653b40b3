<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form
        v-if="!isLoadingGetDetail"
        ref="detailForm"
        :model="detailData"
        :rules="detailFormRules"
        label-colon
        :label-width="120"
      >
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.告警名称.label"
                :prop="formItemsConfig.告警名称.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.告警名称.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.告警级别.label"
                :prop="formItemsConfig.告警级别.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.告警级别.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.告警级别.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.来源类型.label"
                :prop="formItemsConfig.来源类型.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.来源类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.来源类型.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.具体来源.label"
                :prop="formItemsConfig.具体来源.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.具体来源.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.具体来源.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.对象类型.label"
                :prop="formItemsConfig.对象类型.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.对象类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.对象类型.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.对象.label" :prop="formItemsConfig.对象.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.对象.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.是否评估.label"
                :prop="formItemsConfig.是否评估.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.是否评估.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.是否评估.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.处理结果.label"
                :prop="formItemsConfig.处理结果.prop"
              >
                <SelectDic
                  v-model.trim="detailData[formItemsConfig.处理结果.prop]"
                  :disabled="detailFormConfig.isReadonly"
                  :list="formItemsConfig.处理结果.list"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem :label="formItemsConfig.处理人.label" :prop="formItemsConfig.处理人.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.处理人.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card title="上报信息" dis-hover :bordered="false" v-if="detailData['sourceDetail'] == 5">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.上报人.label" :prop="formItemsConfig.上报人.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.上报人.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.联系方式.label"
                :prop="formItemsConfig.联系方式.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.联系方式.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem
                :label="formItemsConfig.告警详情.label"
                :prop="formItemsConfig.告警详情.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.告警详情.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card title="源攻击信息" dis-hover :bordered="false" v-if="detailData['sourceDetail'] == 1">
          <Row>
            <Col span="12">
              <FormItem :label="formItemsConfig.源IP.label" :prop="formItemsConfig.源IP.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.源IP.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.攻击次数.label"
                :prop="formItemsConfig.攻击次数.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.攻击次数.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card
          title="多车关联信息"
          dis-hover
          :bordered="false"
          v-if="detailData['sourceDetail'] == 2"
        >
          <Row>
            <Col span="12">
              <FormItem
                :label="formItemsConfig.事件类型.label"
                :prop="formItemsConfig.事件类型.prop"
              >
                <Input
                  v-model.trim="detailData[formItemsConfig.事件类型.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="攻击次数" :prop="formItemsConfig.攻击次数2.prop">
                <Input
                  v-model.trim="detailData[formItemsConfig.攻击次数2.prop]"
                  :disabled="detailFormConfig.isReadonly"
                />
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card
          title="复杂分析信息"
          dis-hover
          :bordered="false"
          v-if="detailData['sourceDetail'] == 3"
        >
          <Table
            ref="listTable"
            :columns="listColumns"
            :data="listData"
            :loading="listLoading"
            stripe
          ></Table>
        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">
            保存
          </Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import LoadingDetail from '@/components/loading/loading-detail.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import SelectDic from '@/components/select/select-dic.vue';

import mixinsPageForm from '@/mixins/mixinsPageForm';
import mixinsPageList from '@/mixins/mixinsPageList';

import { getDetailInfo, relationItem } from '@/api/safety/warn-event-manage';

const needEvaluationList = [
  {
    value: 1,
    label: '是',
  },
  {
    value: 2,
    label: '否',
  },
];

const evaluationResultList = [
  {
    value: 1,
    label: '生成工单',
  },
  {
    value: 2,
    label: '忽略',
  },
];

const alarmLevelList = [
  {
    value: 1,
    label: '高',
  },
  {
    value: 2,
    label: '中',
  },
  {
    value: 3,
    label: '低',
  },
];

export default {
  mixins: [mixinsPageForm, mixinsPageList],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getDetailInfo,
        },
        getList: {
          apiFun: relationItem,
        },
      },
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'warn-event-manage-list',
        },
      },
      // 页面配置-表单
      pageItemsConfig: {
        告警名称: {
          editFormItem: {
            valuekey: 'alarmName',
          },
        },
        告警级别: {
          editFormItem: {
            valuekey: 'alarmLevel',
            list: alarmLevelList,
          },
        },
        来源类型: {
          editFormItem: {
            valuekey: 'sourceType',
            list: [
              {
                value: 1,
                label: '内部规则',
              },
              {
                value: 2,
                label: '外部情报源',
              },
              {
                value: 3,
                label: '手工录入',
              },
            ],
          },
        },
        具体来源: {
          editFormItem: {
            valuekey: 'sourceDetail',
            list: [
              {
                value: '1',
                label: '攻击源分析',
              },
              {
                value: '2',
                label: '多车关联分析',
              },
              {
                value: '3',
                label: '复杂攻击分析',
              },
              {
                value: '4',
                label: '外部情报源',
              },
              {
                value: '5',
                label: '手工录入',
              },
            ],
          },
        },
        对象类型: {
          editFormItem: {
            valuekey: 'targetType',
            list: [
              {
                value: '1',
                label: '品牌',
              },
              {
                value: '2',
                label: '车型',
              },
              {
                value: '3',
                label: '车辆',
              },
            ],
          },
        },
        对象: {
          editFormItem: {
            valuekey: 'targetIds',
          },
        },
        是否评估: {
          editFormItem: {
            valuekey: 'needEvaluation',
            list: needEvaluationList,
          },
        },
        处理结果: {
          editFormItem: {
            valuekey: 'evaluationResult',
            list: evaluationResultList,
          },
        },
        处理人: {
          editFormItem: {
            valuekey: 'evaluatorId',
          },
        },
        上报人: {
          editFormItem: {
            valuekey: 'reporter',
          },
        },
        联系方式: {
          editFormItem: {
            valuekey: 'reporterContact',
          },
        },
        告警详情: {
          editFormItem: {
            valuekey: 'manualAlarmInfo',
          },
        },
        源IP: {
          editFormItem: {
            valuekey: 'sourceIp',
          },
        },
        攻击次数: {
          editFormItem: {
            valuekey: 'attackCount',
          },
        },
        事件类型: {
          editFormItem: {
            valuekey: 'eventTypeId',
          },
          listColumn: {
            valuekey: 'eventType',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        事件id: {
          listColumn: {
            valuekey: 'id',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        受攻击设备: {
          listColumn: {
            valuekey: 'attackedDevice',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        攻击时间: {
          listColumn: {
            valuekey: 'eventTime',
            minWidth: 120,
            renderType: 'YMDHMS',
          },
        },
        攻击顺序: {
          listColumn: {
            valuekey: 'attackOrder',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['事件id', '事件类型', '受攻击设备', '攻击时间', '攻击顺序'];
      return columns;
    },
  },
  created() {
    const record = JSON.parse(this.$route.params.record);
    console.log(record);
    // const replactRequestData = ({ requestData }) => ({
    //   ...requestData,
    //   id: record.id,
    // });
    // this.pageConfigAPI.getDetailData.replactRequestData = replactRequestData;

    const replaceRequestData = (requestData) => ({
      ...requestData,
      alarmId: record.id,
    });
    this.pageConfigAPI.getList.replaceRequestData = replaceRequestData;

    // relationItem({
    //   alarmId: this.$route.params.record.id,
    // }).then((res) => {
    //   this.listData = [res.data];
    // });

    this.initDetailPage();
    this.initPage();

    console.log(this.detailData.sourceDetail);
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
