<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm
        ref="searchForm"
        :searchFormInitData="searchFormInitData"
        :searchFormListOrder="searchFormListOrder"
        :searchFormItems="searchFormItems"
        @on-search="handleSearch"
        @reset-search="handleReset"
      />
      <!-- 列表 -->
      <div class="content">
        <div class="content-btn">
          <Button v-if="pagePermission.addListItems" type="primary" @click="addItem()">新增</Button>
        </div>
        <div class="content-table">
          <Table
            ref="listTable"
            :columns="listColumns"
            :data="listData"
            :loading="listLoading"
            stripe
          >
            <template v-slot:action="{ row }">
              <span
                v-if="pagePermission.showListItem"
                class="action-btn"
                @click="showListItem({ record: row, isReadonly: true })"
              >
                详情
              </span>
              <span
                v-if="pagePermission.relationsItem"
                class="action-btn"
                @click="relationsItem(row)"
              >
                关联事件
              </span>
              <span v-if="pagePermission.dealItems" class="action-btn" @click="dealItems(row)">
                处置
              </span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page
          :total="listConfig.total"
          :model-value="listConfig.page"
          :page-size="listConfig.size"
          show-sizer
          transfer
          show-elevator
          show-total
          @on-page-size-change="changeSize"
          @on-change="changePage"
        />
      </Row>
    </div>
  </div>
</template>

<script>
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { getTableList1 } from '@/api/safety/warn-event-manage';

import { itemConfig } from './config';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getTableList1,
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'warn-event-manage-detail',
          permCode: 'ivsmp.warn-event-manage.detail',
        },
        addListItems: {
          routerName: 'warn-event-manage-add',
          permCode: 'ivsmp.warn-event-manage.add',
        },
        relationsItem: {
          routerName: 'warn-event-manage-relations',
          permCode: 'ivsmp.warn-event-manage.relations',
        },
        dealItems: {
          routerName: 'warn-event-manage-deal',
          permCode: 'ivsmp.warn-event-manage.deal',
        },
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['来源类型', '源IP', '对象类型', '是否评估', '处置结果'],
      pageItemsConfig: itemConfig.gjy,
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = [
        '告警id',
        '告警名称',
        '告警时间',
        '告警等级',
        '来源类型',
        '源IP',
        '攻击次数',
        '对象类型',
        '是否评估',
        '处置结果',
        '处置人',
      ];
      if (
        this.pagePermission.showListItem ||
        this.pagePermission.relationsItem ||
        this.pagePermission.relationsItemdealItems
      ) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
  },
  methods: {
    dealItems(row) {
      this.$emit('deal', row);
    },
    relationsItem(row) {
      this.$emit('relations', row);
    },
    addItem() {
      this.$emit('addItem', '1');
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
