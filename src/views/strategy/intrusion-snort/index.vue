<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm ref="searchForm" :searchFormInitData="searchFormInitData" :searchFormListOrder="searchFormListOrder"
        :searchFormItems="searchFormItems" @on-search="handleSearch" @reset-search="handleReset" />
      <!-- 列表 -->
      <div class="content">
        <div v-if="pagePermission.addListItems || pagePermission.delListItems" class="content-btn">
          <Button v-if="pagePermission.addListItems" type="primary" @click="addListItem()">添加</Button>
          <Button v-if="pagePermission.delListItems" type="error" ghost @click="deleteListItems()">删除</Button>
        </div>
        <div class="content-table">
          <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
            <template v-slot:action="{ row }">
              <span v-if="pagePermission.showListItem" class="action-btn"
                @click="showListItem({ record: row })">查看详情</span>
              <span v-if="pagePermission.editListItem" class="action-btn"
                @click="editListItem({ record: row })">修改</span>
            </template>
          </Table>
        </div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page :total="listConfig.total" :model-value="listConfig.page" :page-size="listConfig.size" show-sizer transfer
          show-elevator show-total @on-page-size-change="changeSize" @on-change="changePage" />
      </Row>
    </div>
  </div>
</template>

<script>
import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';
import { deleteIntrusionSnort, getIntrusionSnortList } from '@/api/intrusion-snort';
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  data() {
    return {
      // 通用-搜索
      brandList: [],
      modelList: [],
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getIntrusionSnortList,
        },
        delListItems: {
          apiFun: deleteIntrusionSnort,
          confirm: {
            content: '是否确认删除所选数据？？',
            comments: '删除后不可恢复，如需再次使用该数据，需重新添加！',
          },
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'intrusion-snort-detail',
          permCode: 'ivsmp.strategy-mange.intrusion-snort-list',
        },
        addListItems: {
          routerName: 'intrusion-snort-add',
          permCode: 'ivsmp.strategy-mange.intrusion-snort-add',
        },
        editListItem: {
          routerName: 'intrusion-snort-edit',
          permCode: 'ivsmp.strategy-mange.intrusion-snort-edit',
        },
        delListItems: {
          permCode: 'ivsmp.strategy-mange.intrusion-snort-del',
        },
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['品牌', '车型', '事件类型'],

    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['策略名', '版本号', '事件类型', '版本更新说明', '规则数', '状态', '发布时间',];
      if (this.pagePermission.delListItems) {
        columns.unshift('CHECKBOX');
      }
      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
    pageItemsConfig: function () {
      return {
        品牌: {
          searchFormItem: {
            valuekey: 'brandId',
            formItemType: 'Select',
            optionList: this.brandList,
          },
        },
        车型: {
          searchFormItem: {
            valuekey: 'carModelId',
            formItemType: 'Select',
            optionList: this.modelList,
          }
        },
        策略名: {
          listColumn: {
            valuekey: 'snortName',
            minWidth: 80,
            render: (h, params) => {
              const model = this.modelList.find(item => item.id === params.row.carModelId);
              console.log(model);
              if (model) {
                return h('div', model.modelName);
              }

              return h('div', params.row.carModelId);
            },
          },

        },
        版本号: {
          listColumn: {
            valuekey: 'version',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        事件类型: {
          searchFormItem: {
            valuekey: 'ruleName'
          },
          listColumn: {
            valuekey: 'ruleName',
            minWidth: 80,
          },
        },
        版本更新说明: {
          listColumn: {
            valuekey: 'description',
            minWidth: 80,
          },
        },
        状态: {
          listColumn: {
            valuekey: 'status',
            width: 120,
          },
        },
        发布时间: {
          listColumn: {
            valuekey: 'updatedAt',
            renderType: 'YMDHMS',
            width: 170,
          },
        },

      };
    }
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
    this.getBrand();
    this.getModel();
  },
  methods: {
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach(item => {
          item.label = item.name;
          item.value = item.id;
        });
        this.pageItemsConfig.品牌.searchFormItem.optionList = this.brandList;
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res;
        this.modelList.forEach(item => {
          item.label = item.modelName;
          item.value = item.id;
        });
        this.pageItemsConfig.车型.searchFormItem.optionList = this.modelList;
      }
    },
    deleteListItems() {
      const { confirm = {} } = this.pageConfigAPI.delListItems;
      const {
        title = '操作确认',
        content = '是否确认删除所选数据？',
        comments,
        noSelectedMsg = '请先选择要删除的内容',
      } = confirm;

      const selection = this.$refs.listTable.getSelection();

      if (selection.length > 0) {
        this.$confirm({
          title,
          content,
          comments,
          onOk: async () => {
            for (const { id } of selection) {
              if (!id) {
                this.$Message.error('删除失败，ID不能为空！');
                return;
              }

              await deleteIntrusionSnort({ id });
            }
            this.refreshPage();
          },
        });
      } else {
        this.$Message.warning(noSelectedMsg);
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
