<template>
  <div>
    <typical-page-header :header-title="pageTitle" />
    <div class="form">
      <LoadingDetail :isLoading="isLoadingGetDetail" />
      <Form v-if="!isLoadingGetDetail" ref="detailForm" :model="detailData" :rules="detailFormRules" label-colon
        :label-width="120">
        <Card title="基础信息" dis-hover :bordered="false">
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.规则名.label" :prop="formItemsConfig.规则名.prop">
              <Input v-model.trim="detailData[formItemsConfig.规则名.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.规则名.placeholder" :maxlength="formItemsConfig.规则名.maxlength" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem :label="formItemsConfig.车辆品牌.label" :prop="formItemsConfig.车辆品牌.prop">
              <SelectDic
                v-model:modelValue="detailData[formItemsConfig.车辆品牌.prop]"
                :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.车辆品牌.placeholder"
                :dicType="formItemsConfig.车辆品牌.dicType"
                :list="formItemsConfig.车辆品牌.list"
                @change-data="changeBrand" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.车型.label" :prop="formItemsConfig.车型.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.车型.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.车型.placeholder"
                :dicType="formItemsConfig.车型.dicType" :list="formItemsConfig.车型.list" />
            </FormItem>
            </Col>

            <Col span="12">
            <FormItem :label="formItemsConfig.类型.label" :prop="formItemsConfig.类型.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.类型.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.类型.placeholder"
                :dicType="formItemsConfig.类型.dicType" />
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem :label="formItemsConfig.状态.label" :prop="formItemsConfig.状态.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.状态.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.状态.placeholder"
                :dicType="formItemsConfig.状态.dicType" />
            </FormItem>
            </Col>

            <Col span="12">
            <FormItem :label="formItemsConfig.生效策略.label" :prop="formItemsConfig.生效策略.prop">
              <SelectDic v-model:modelValue="detailData[formItemsConfig.生效策略.prop]"
                :disabled="detailFormConfig.isReadonly" :placeholder="formItemsConfig.生效策略.placeholder"
                :dicType="formItemsConfig.生效策略.dicType" @change-data="handleStrategyChange" />
            </FormItem>
            </Col>

            <Col span="12" v-if="['recurring', 'once'].includes(detailData[formItemsConfig.生效策略.prop])">
            <FormItem :label="formItemsConfig.生效时间.label" :prop="formItemsConfig.生效时间.prop">
              <TimePicker v-if="detailData[formItemsConfig.生效策略.prop] === 'recurring'" type="timerange" format="HH:mm"
                placement="bottom-end" v-model="detailData[formItemsConfig.生效时间.prop]"
                :disabled="detailFormConfig.isReadonly" />
              <Input v-if="detailData[formItemsConfig.生效策略.prop] === 'once'"
                v-model:modelValue="detailData[formItemsConfig.生效时间.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.生效时间.placeholder" />
            </FormItem>
            </Col>

            <Col span="24">
            <FormItem label="生效条件">
              <Checkbox v-model="detailData[formItemsConfig.车辆熄火时.prop]">车辆熄火时</Checkbox>
              <Checkbox v-model="detailData[formItemsConfig.高风险警告时.prop]">高风险警告时</Checkbox>
            </FormItem>
            </Col>
            <Col span="24">
            <FormItem :label="formItemsConfig.规则说明.label" :prop="formItemsConfig.规则说明.prop">
              <Input v-model.trim="detailData[formItemsConfig.规则说明.prop]" :disabled="detailFormConfig.isReadonly"
                :placeholder="formItemsConfig.规则说明.placeholder" type="textarea" :rows="2"
                :maxlength="formItemsConfig.规则说明.maxlength" show-word-limit />
            </FormItem>
            </Col>
          </Row>
        </Card>
      </Form>
      <FooterToolbar>
        <template v-if="isLoadingGetDetail || detailFormConfig.isReadonly">
          <Button type="default" @click="goBack({ skipConfirmLeave: true })">返回</Button>
        </template>
        <template v-else>
          <Button type="default" @click="goBack">取消</Button>
          <Button type="primary" :loading="isLoadingCommitDetail" @click="commitDetailData">保存</Button>
        </template>
      </FooterToolbar>
    </div>
  </div>
</template>

<script>
import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';
import { createFirewall, getFirewallDetail, updateFirewall } from '@/api/firewall';
import LoadingDetail from '@/components/loading/loading-detail.vue';
import SelectDic from '@/components/select/select-dic-label.vue';
import TypicalPageHeader from '@/components/typical-page-header/index.vue';
import { LENGTH_MAX_INPUT, LENGTH_MAX_INPUT_LONG } from '@/define';
import mixinsPageForm from '@/mixins/mixinsPageForm';
import { Checkbox } from 'view-ui-plus';

export default {
  mixins: [mixinsPageForm],
  components: { LoadingDetail, TypicalPageHeader, SelectDic },
  data() {
    return {
      // 通用-表单
      brandList: [],
      modelList: [],
      isLoadingGetDetail: true,
      detailData: {},
      detailFormConfig: {},
      isLoadingCommitDetail: false,
      // 页面配置-API
      pageConfigAPI: {
        getDetailData: {
          apiFun: getFirewallDetail,
        },
        addDetailData: {
          apiFun: createFirewall,
          successMsg: '防火墙规则加成功',
          replactRequestData: ({ requestData }) => {
            return {
              ...requestData,
              effectiveTime: Array.isArray(requestData.effectiveTime) ? requestData.effectiveTime.join('-') : requestData.effectiveTime
            };
          }
        },
        editDetailData: {
          apiFun: updateFirewall,
          successMsg: '防火墙规则修改成功',
          replactRequestData: ({ requestData }) => {
            return {
              ...requestData,
              effectiveTime: Array.isArray(requestData.effectiveTime) ? requestData.effectiveTime.join('-') : requestData.effectiveTime
            };
          }
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        goBack: {
          routerName: 'firewall-list',
        },
      },
      // 页面配置-表单
    };
  },
  computed: {
    pageItemsConfig() {
      return {
        规则名: {
          editFormItem: {
            valuekey: 'name',
            formRules: [{ ruleType: 'required' }, { ruleType: 'length', max: LENGTH_MAX_INPUT }],
          },
        },
        车辆品牌: {
          editFormItem: {
            valuekey: 'brandId',
            formRules: [{ ruleType: 'required' }],
            list: this.brandList,
          },
        },
        车型: {
          editFormItem: {
            valuekey: 'carModelId',
            formRules: [{ ruleType: 'required' }],
            list: this.modelList,
          },
        },
        类型: {
          editFormItem: {
            valuekey: 'type',
            formRules: [{ ruleType: 'required' }],
            dicType: 3005,
          },
        },
        状态: {
          editFormItem: {
            valuekey: 'status',
            formRules: [
              { ruleType: 'required', },
            ],
            dicType: 3004,
          },
        },
        生效策略: {
          editFormItem: {
            valuekey: 'effectiveStrategy',
            formRules: [
              { ruleType: 'required', },
            ],
            dicType: 3006,
          },
        },
        生效时间: {
          editFormItem: {
            valuekey: 'effectiveTime',
            // formRules: [{ ruleType: 'required' }],
            type: 'date',
          },
        },
        车辆熄火时: {
          editFormItem: {
            valuekey: 'effectivePoweroff',
          },
        },
        高风险警告时: {
          editFormItem: {
            valuekey: 'effectiveWarnning',
          },
        },
        规则说明: {
          editFormItem: {
            valuekey: 'description',
            formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT_LONG }],
          },
        },
      };
    }
  },
  created() {
    this.initDetailPage();
    this.getBrand();
  },
  methods: {
    changeBrand(e) {
      if (e) {
        this.getModel();
      } else {
        this.modelList = [];
        this.detailData.carModelId = '';
      }
    },
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach(item => {
          item.label = item.id;
          item.remark = item.name;
        });
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res;
        this.modelList.forEach(item => {
          item.label = item.id;
          item.remark = item.modelName;
        });
      }
    },
    handleChange(e) {
      this.detailData.effectiveTime = e;
    },
    handleStrategyChange(strategy) {
      if (strategy === 'recurring') {
        this.detailData.effectiveTime = [];
      } else if (strategy === 'once') {
        this.detailData.effectiveTime = '';
      }
    },
    // handleTimeRangeChange(value) {
    //   console.log('Time range changed:', value);
    //   if (Array.isArray(value) && value.length === 2) {
    //     this.detailData.effectiveTime = value.join('-');
    //   } else {
    //     this.detailData.effectiveTime = '';
    //   }
    // }
  }
};
</script>

<style lang="less" scoped>
@import '~@/styles/form-page.less';
</style>
