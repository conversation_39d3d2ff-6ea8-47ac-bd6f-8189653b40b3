<template>
  <Tag>
    {{this.dicList.find(item => item.label === status)?.remark || status}}
  </Tag>
</template>
<script>
import { getDicData } from '@/api/common';
import { Tag } from 'view-ui-plus';

export default {
  props: {
    dicType: {
      type: [String, Number],
      default: '',
    },
    status: {
      type: [String],
      default: '',
    },
  },
  data() {
    return {
      dicList: [],
    };
  },
  created() {
    if (this.dicType) {
      this.getOptionList();
    }
  },
  methods: {
    getOptionList() {
      getDicData({ dicType: this.dicType }).then((response) => {
        if (this.spliceOption) {
          response = response.filter((item) => {
            return !this.spliceOption.includes(item.value);
          });
        }
        this.dicList = response;
        console.log('dicList', this.dicList);
      });
    },
  },
};
</script>
