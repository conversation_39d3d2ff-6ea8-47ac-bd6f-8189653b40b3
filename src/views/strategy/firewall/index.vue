<template>
  <div class="page-wrap">
    <div class="base-table">
      <!-- 搜索 -->
      <SearchForm ref="searchForm" :searchFormInitData="searchFormInitData" :searchFormListOrder="searchFormListOrder"
        :searchFormItems="searchFormItems" @on-search="handleSearch" @reset-search="handleReset" />
      <div class="search-wrap">
        <Button type="primary" v-auth="['ivsmp.strategy-mange.firewall-add']" @click="addListItem()">新增规则</Button>
      </div>
      <!-- 列表 -->
      <div class="content">
        <Collapse simple v-if="listData.length > 0">
          <Panel v-for="item in listData" :name="item.id" :key="item.id">

            <div class="panel-header">
              <div class="panel-name">{{ [item.name, item.brandName, item.carName].join('、') }}
                <Status :status="item.type" dicType="3005" />
                <Status :status="item.status" dicType="3004" />
              </div>
              <div class="panel-actions">
                <Button @click="(e) => copyItem(e, item.id)">拷贝</Button>
              </div>
            </div>

            <template #content>
              <StrategyTable :id="item.id" />
            </template>
          </Panel>
        </Collapse>
        <div v-else class="empty">暂无数据</div>
      </div>
      <Row justify="end" type="flex" class="page">
        <Page :total="listConfig.total" :model-value="listConfig.page" :page-size="listConfig.size" show-sizer transfer
          show-elevator show-total @on-page-size-change="changeSize" @on-change="changePage" />
      </Row>
    </div>
  </div>
</template>

<script>
import { getAllBrandApi } from '@/api/asset/car-brand';
import { getAllModelsApi } from '@/api/asset/car-model';
import { copyFirewall, deleteFirewall, getFirewallList } from '@/api/firewall';
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';
import { Button } from 'view-ui-plus';
import Status from './status.vue';
import StrategyTable from './strategy-table.vue';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
    StrategyTable,
    Status,
  },
  data() {
    return {
      // 通用-搜索
      brandList: [],
      modelList: [],
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getFirewallList,
        },
        delListItems: {
          apiFun: deleteFirewall,
          confirm: {
            content: '是否确认删除套餐？',
            comments: '删除后不可恢复，如需再次使用该套餐，需重新添加！',
          },
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'firewall-detail',
          permCode: 'monitor.demo.demo-list.detail',
        },
        addListItems: {
          routerName: 'firewall-list-add',
          permCode: 'ivsmp.strategy-mange.firewall-add',
        },
        editListItem: {
          routerName: 'firewall-list-edit',
          permCode: 'ivsmp.strategy-mange.firewall-edit',
        },
        delListItems: {
          permCode: 'ivsmp.strategy-mange.firewall-del',
        },
      },
      // 页面配置-列表、搜索
      searchFormListOrder: ['品牌', '车型', '类型'],
      pageItemsConfig: {
        品牌: {
          searchFormItem: {
            valuekey: 'brandId',
            formItemType: 'Select',
            optionList: this.brandList,
          }
        },
        车型: {
          searchFormItem: {
            valuekey: 'carModelId',
            formItemType: 'Select',
            optionList: this.modelList,
          }
        },
        类型: {
          searchFormItem: {
            valuekey: 'type',
            formItemType: 'Select',
            optionList: [
              { label: '传统网络', value: 'TRADITIONAL' },
              { label: '总线', value: 'CAN' },
              { label: '以太网', value: 'ETHERNET' },
            ],
          }
        },
        策略类型: {
          searchFormItem: {
            valuekey: 'packageName',
          },
          listColumn: {
            valuekey: 'packageName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        协议: {
          searchFormItem: {
            valuekey: 'customName',
          },
          listColumn: {
            valuekey: 'carCompany',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        源IP: {
          searchFormItem: {
            valuekey: 'packageType',
            formItemType: 'Select',
            dicType: 1301,
          },
          listColumn: {
            valuekey: 'packageTypeName',
            minWidth: 80,
          },
        },
        源Mac: {
          searchFormItem: {
            valuekey: 'flowType',
            formItemType: 'Select',
            dicType: 1307,
          },
          listColumn: {
            valuekey: 'flowTypeName',
            minWidth: 80,
          },
        },
        源端口: {
          listColumn: {
            label: '售价 (元)',
            valuekey: 'price',
            width: 100,
            renderType: 'number',
            renderPrecision: 2,
          },
        },
        目的IP: {
          listColumn: {
            label: '优惠价 (元)',
            valuekey: 'bestPrice',
            width: 120,
            renderType: 'number',
            renderPrecision: 2,
          },
        },
        目的Mac: {
          listColumn: {
            valuekey: 'createTime',
            width: 170,
          },
        },
        目的端口: {
          listColumn: {
            valuekey: 'updateTime',
            width: 170,
          },
        },
        优先级顺序: {
          listColumn: {
            valuekey: 'updateTime',
            width: 170,
          },
        },
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['套餐名称', '适用客户', '套餐类型', '流量类型', '售价', '优惠价', '流量大小', '创建时间', '最近修改时间'];
      if (this.pagePermission.delListItems) {
        columns.unshift('CHECKBOX');
      }
      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },

  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
    this.getBrand();
    this.getModel();
  },
  methods: {
    async getBrand() {
      const res = await getAllBrandApi({});
      if (res && res.length) {
        this.brandList = res;
        this.brandList.forEach(item => {
          item.label = item.name;
          item.value = item.id;
        });
        this.pageItemsConfig.品牌.searchFormItem.optionList = this.brandList;
      }
    },
    async getModel() {
      const res = await getAllModelsApi({});
      if (res && res.length) {
        this.modelList = res;
        this.modelList.forEach(item => {
          item.label = item.modelName;
          item.value = item.id;
        });
        this.pageItemsConfig.车型.searchFormItem.optionList = this.modelList;
      }
    },
    async copyItem(e, id) {
      e.stopPropagation();
      await copyFirewall({ id });
      this.$message.success('拷贝成功');
      this.refreshPage();
    }
  }
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';

.search-wrap {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 12px;
  padding-left: 12px;
  padding-right: 12px;
}

.empty {
  display: flex;
  justify-content: center;
  align-items: center;
}

/deep/.ivu-collapse-header {
  display: flex;
  padding: 10px;
}

.panel-header {
  flex: 1;
  display: flex;
  gap: 16px;
  width: max-content;
  justify-content: space-between;
  align-items: center;
  padding: 8px;

  .panel-name {
    font-weight: bold;
  }

  .panel-actions {
    display: flex;
    gap: 8px;
  }
}
</style>
