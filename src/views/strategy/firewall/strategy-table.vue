<template>
  <div class="base-table">
    <!-- 搜索 -->
    <SearchForm ref="searchForm" :searchFormInitData="searchFormInitData" :searchFormListOrder="searchFormListOrder"
      :searchFormItems="searchFormItems" @on-search="handleSearch" @reset-search="handleReset" />
    <!-- 列表 -->
    <div class="content">
      <div v-if="pagePermission.addListItems || pagePermission.delListItems" class="content-btn">
        <Button v-if="pagePermission.addListItems" type="primary" @click="addRule()">添加</Button>
        <Button v-if="pagePermission.delListItems" type="error" ghost @click="deleteListItems()">删除</Button>
        <Button v-if="pagePermission.addListItems" @click="publishRule()">发布</Button>
      </div>
      <div class="content-table">
        <Table ref="listTable" :columns="listColumns" :data="listData" :loading="listLoading" stripe>
          <template v-slot:action="{ row }">
            <span v-if="pagePermission.showListItem" class="action-btn"
              @click="showListItem({ record: row })">查看详情</span>
            <span v-if="pagePermission.editListItem" class="action-btn" @click="editListItem({ record: row })">修改</span>
          </template>
        </Table>
      </div>
    </div>
    <Row justify="end" type="flex" class="page">
      <Page :total="listConfig.total" :model-value="listConfig.page" :page-size="listConfig.size" show-sizer transfer
        show-elevator show-total @on-page-size-change="changeSize" @on-change="changePage" />
    </Row>
  </div>
</template>

<script>
import { deleteFirewallRule, getFirewallRuleList, publishFirewallRule } from '@/api/firewall-rule';
import SearchForm from '@/components/form/search-form';
import mixinsPageList from '@/mixins/mixinsPageList';

export default {
  mixins: [mixinsPageList],
  components: {
    SearchForm,
  },
  props: {
    id: Number,
  },
  data() {
    return {
      // 通用-搜索
      searchFormInitData: {},
      // 通用-列表
      listLoading: true,
      listConfig: {
        total: 0,
        size: 10,
        page: 1,
      },
      listData: [],
      // 页面配置-API
      pageConfigAPI: {
        getList: {
          apiFun: getFirewallRuleList,
          replaceRequestData: ({ requestData }) => {
            return {
              ...requestData,
              strategyId: this.id,
            }
          }
        },
        delListItems: {
          apiFun: deleteFirewallRule,
          confirm: {
            content: '是否确认删除所选数据？？',
            comments: '删除后不可恢复，如需再次使用该数据，需重新添加！',
          },
        },
      },
      // 页面配置-Button
      pageConfigButton: {
        showListItem: {
          routerName: 'firewall-list-rule-detail',
          permCode: 'ivsmp.strategy-mange.firewall-rule-detail',
        },
        addListItems: {
          routerName: 'firewall-list-rule-add',
          permCode: 'ivsmp.strategy-mange.firewall-rule-add',
        },
        editListItem: {
          routerName: 'firewall-list-rule-edit',
          permCode: 'ivsmp.strategy-mange.firewall-rule-edit',
        },
        delListItems: {
          permCode: 'ivsmp.strategy-mange.firewall-rule-del',
        },
      },
      // 页面配置-列表、搜索
      // searchFormListOrder: ['搜索',],
      pageItemsConfig: {
        // 搜索: {
        //   searchFormItem: {
        //     valuekey: 'searchKey',
        //   }
        // },
        策略类型: {
          listColumn: {
            valuekey: 'ruleName',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        协议: {
          listColumn: {
            valuekey: 'protocol',
            minWidth: 120,
            renderType: 'ellipsis',
          },
        },
        源IP: {
          listColumn: {
            valuekey: 'srcIp',
            minWidth: 80,
          },
        },
        源Mac: {
          listColumn: {
            valuekey: 'srcMac',
            minWidth: 80,
          },
        },
        源端口: {
          listColumn: {
            valuekey: 'srcPort',
            minWidth: 80,
          },
        },
        目的IP: {
          listColumn: {
            valuekey: 'dstIp',
            minWidth: 80,
          },
        },
        目的Mac: {
          listColumn: {
            valuekey: 'dstMac',
            minWidth: 80,
          },
        },
        目的端口: {
          listColumn: {
            valuekey: 'dstPort',
            minWidth: 80,
          },
        },
        优先级顺序: {
          listColumn: {
            valuekey: 'priority',
            minWidth: 80,
          },
        },
      },
    };
  },
  computed: {
    listColumnsOrder: function () {
      let columns = ['策略类型', '协议', '源IP', '源Mac', '源端口', '目的IP', '目的Mac', '目的端口', '优先级顺序'];
      if (this.pagePermission.delListItems) {
        columns.unshift('CHECKBOX');
      }
      if (this.pagePermission.showListItem || this.pagePermission.editListItem) {
        columns.push('ACTION');
      }
      return columns;
    },
  },
  created() {
    this.initSearchFormData();
  },
  mounted() {
    this.initPage();
  },
  methods: {
    /**
    * 新增
    */
    addRule() {
      const { addListItems = {} } = this.pageConfigButton || {};
      if (addListItems.routerName) {
        this.$router.push({
          name: addListItems.routerName,
          params: {
            toChildrenPage: true,
            savePageData: this.savePageData(),
          },
          query: {
            strategyId: this.id,
          }
        });
      }
    },
    deleteListItems() {
      const { confirm = {} } = this.pageConfigAPI.delListItems;
      const {
        title = '操作确认',
        content = '是否确认删除所选数据？',
        comments,
        noSelectedMsg = '请先选择要删除的内容',
      } = confirm;

      const selection = this.$refs.listTable.getSelection();

      if (selection.length > 0) {
        this.$confirm({
          title,
          content,
          comments,
          onOk: async () => {
            for (const { id } of selection) {
              if (!id) {
                this.$Message.error('删除失败，ID不能为空！');
                return;
              }

              await deleteFirewallRule({ id });
            }
            this.refreshPage();
          },
        });
      } else {
        this.$Message.warning(noSelectedMsg);
      }
    },
    publishRule() {
      console.log('发布规则');

      const {
        title = '操作确认',
        content = '是否确认发布所选数据？',
        comments,
        noSelectedMsg = '请先选择要发布的内容',
      } = confirm;

      const selection = this.$refs.listTable.getSelection();
      if (selection.length > 0) {
        this.$confirm({
          title,
          content,
          comments,
          onOk: async () => {
            await publishFirewallRule({ ids: selection.map(item => item.id) });
            this.$message.success('发布成功');
            // 刷新页面
            this.refreshPage();
          },
        });
      } else {
        this.$Message.warning(noSelectedMsg);
      }
    }

  }
};
</script>

<style lang="less" scoped>
@import '~@/styles/list-search-page.less';
</style>
