<template>
  <div>
    <MyEcharts width="100%" height="140px" :options="options1"></MyEcharts>
  </div>
</template>

<script>
import MyEcharts from './MyEcharts.vue';
import * as echarts from 'echarts';
export default {
  data() {
    return {
      options1: null,
    };
  },
  components: {
    MyEcharts,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        this.options1 = this.getPiechartOption();
      },
      deep: true,
    },
  },
  mounted() {
    this.options1 = this.getPiechartOption();
  },
  methods: {
    getPiechartOption() {
      const data = this.data.optionsData;

      // 判断数据是否为零或为空
      const hasData = data.some((item) => item.value > 0);
      const validDataCount = data.filter((item) => item.value > 0).length;
      const chartData = hasData ? data : [{ name: '无数据', value: 1e-10 }]; // 使用一个小数值来确保闭合

      let option = {
        color: hasData
          ? ['rgba(203, 58, 58, 1)', 'rgba(54, 255, 204, 1)','rgba(137, 147, 253, 1)']
          : ['rgba(169, 169, 169, 0.8)'], // 灰色
        title: {
          text: this.data.title,
          top: 'center',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'bold',
            color: '#fff',
          },
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          borderColor: 'rgba(0, 0, 0, 0.7)',
          textStyle: {
            color: '#fff',
          },
          formatter: function (params) {
            return `${params.name}: ${params.percent}%`;
          },
        },
        series: [
          {
            type: 'pie',
            padAngle: validDataCount > 1 ? 3 : 0, // 只有当数据项大于1时才启用间隔
            radius: ['78%', '90%'], // 保证圆环效果
            avoidLabelOverlap: false,
            label: {
              show: false,
              formatter: function (params) {
                return '20%';
              },
              color: '#fff',
              fontSize: 14,
              fontFamily: 'DIN',
            },
            labelLine: {
              show: true,
              length: 6,
              length2: 6,
              minTurnAngle: 30,
            },
            data: chartData,
            // 确保圆环闭合，移除间隔
            minAngle: 0.1, // 小的角度以防止显示空隙
            startAngle: 90, // 从顶部开始
            clockwise: false, // 保证圆环方向一致
            itemStyle: validDataCount > 1
              ? {
                  borderWidth: 0, // 每个色环上间隔3px
                  borderColor: '#fff', // 间隔色为白色
                }
              : {}, // 数据项小于等于1时不设置边框
          },
        ],
      };

      return option;
    },
  },
};
</script>

<style lang="less" scoped></style>
