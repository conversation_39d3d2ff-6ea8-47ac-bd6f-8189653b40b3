<template>
  <div>
    <MyEcharts width="100%" height="140px" :options="options1" ></MyEcharts>
  </div>
</template>

<script>
import MyEcharts from './MyEcharts.vue';
import * as echarts from 'echarts';

export default {
  data() {
    return {
      options1: null,
      title: this.data.title,  
    };
  },
  components: {
    MyEcharts,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        this.options1 = this.getPiechartOption();
      },
      deep: true,
    },
  },
  mounted() {
    this.options1 = this.getPiechartOption();
  },
  methods: {
    getPiechartOption() {
      const data = this.data.optionsData;

      // 判断数据是否为零或为空
      const hasData = data.some((item) => item.value > 0);
      const validDataCount = data.filter((item) => item.value > 0).length;
      const chartData = hasData ? data : [{ name: '无数据', value: 1e-10 }]; // Use a small value to ensure closing

      let option = {
        color: hasData
          ? ['rgba(187, 107, 201, 1)', 'rgba(24, 188, 53, 1)', 'rgba(148, 228, 141, 1)', 'rgba(58, 210, 255, 1)', 'rgba(137, 147, 253, 1)', 'rgba(54, 255, 204, 1)']
          : ['rgba(169, 169, 169, 0.8)'], // Grey
        title: {
          text: this.title,
          top: 'center',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'bold',
            color: '#fff',
          },
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          borderColor: 'rgba(0, 0, 0, 0.7)',
          textStyle: {
            color: '#fff',
          },
          formatter: function (params) {
            return `${params.name}: ${params.value}`;
          },
        },
        series: [
          {
            type: 'pie',
            padAngle: validDataCount > 1 ? 3 : 0, // Enable spacing only if more than 1 data item
            radius: ['78%', '90%'], // Ensure the ring effect
            avoidLabelOverlap: false,
            label: {
              show: false,
              formatter: function (params) {
                return '20%';
              },
              color: '#fff',
              fontSize: 14,
              fontFamily: 'DIN',
            },
            labelLine: {
              show: true,
              length: 6,
              length2: 6,
              minTurnAngle: 30,
            },
            data: chartData,
            minAngle: 0.1, // Small angle to prevent empty gaps
            startAngle: 90, // Start from the top
            clockwise: false, // Ensure consistent direction for the ring
            itemStyle: validDataCount > 1
              ? {
                  borderWidth: 0, // Gap between each slice
                  borderColor: '#fff', // White gap color
                }
              : {}, // No border if there’s 1 or less data items
            emphasis: {
              itemStyle: {
                opacity: 0.8,
              },
            },
            // Mouseover event callback to update the center title
            mouseover: (params) => {
              console.log(params,"1111");
              this.title = params.name;  // Update the title to the name of the hovered sector
              this.options1.title.text = this.title;  // Update the chart's title dynamically
              this.$forceUpdate();  // Force the update to the view
            },
          },
        ],
      };

      return option;
    },
  },
};
</script>

<style lang="less" scoped></style>
