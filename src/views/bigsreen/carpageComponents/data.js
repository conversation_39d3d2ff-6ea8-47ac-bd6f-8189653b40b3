export default {
  userName: "韩雪冬",
  department: "赈济救护部",
  functionList: [
    {
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "光明行",
    },
    {
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "先心病",
    },
    {
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "博爱送万家",
    },
    {
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "博爱家园",
    },
    {
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "贫困家庭精神病\n患者救助",
    },
    {
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "红十字博爱康复室",
    },
    {
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "院外救助管理",
    },
    {
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "区直机关大病救助",
    },
  ],
  systemList: [
    {
      name: "业务全景视窗",
      icon: require("@/assets/images/nmg/icon2.png"),
    },
    {
      name: "数字驾驶舱",
      icon: require("@/assets/images/nmg/icon3.png"),
    },
    {
      name: "视频会议",
      icon: require("@/assets/images/nmg/icon4.png"),
    },
  ],
  noticeList: [
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
    {
      noticetext: "内蒙古自治区红十字会关于进一步加",
      time: "2020-04-14",
    },
  ],
  iconListMy: [],
  todosList:[{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },{
    tit:'光明行',
    type:'项目办理'
  },],
  iconList: [
    {
      key: "1",
      icon: require("@/assets/images/nmg/gn-p1.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "2",
      icon: require("@/assets/images/nmg/gn-p2.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "3",
      icon: require("@/assets/images/nmg/gn-p3.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "4",
      icon: require("@/assets/images/nmg/gn-p4.png"),
      name: "贫困家庭精神病患者救助",
      state: false,
    },
    {
      key: "5",
      icon: require("@/assets/images/nmg/gn-p5.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "6",
      icon: require("@/assets/images/nmg/gn-p6.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "7",
      icon: require("@/assets/images/nmg/gn-p7.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "8",
      icon: require("@/assets/images/nmg/gn-p8.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "9",
      icon: require("@/assets/images/nmg/gn-p9.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "10",
      icon: require("@/assets/images/nmg/gn-p10.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "11",
      icon: require("@/assets/images/nmg/gn-p11.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "12",
      icon: require("@/assets/images/nmg/gn-p12.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "13",
      icon: require("@/assets/images/nmg/gn-p13.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "14",
      icon: require("@/assets/images/nmg/gn-p14.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "15",
      icon: require("@/assets/images/nmg/gn-p11.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "16",
      icon: require("@/assets/images/nmg/gn-p12.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "17",
      icon: require("@/assets/images/nmg/gn-p13.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "18",
      icon: require("@/assets/images/nmg/gn-p14.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "19",
      icon: require("@/assets/images/nmg/gn-p13.png"),
      name: "光明行",
      state: false,
    },
    {
      key: "20",
      icon: require("@/assets/images/nmg/gn-p14.png"),
      name: "光明行",
      state: false,
    },
  ],
};
