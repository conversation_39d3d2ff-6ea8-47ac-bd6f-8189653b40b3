<template>
	<div :id="uuid" :style="style"></div>
</template>
<script>
	import * as echarts from 'echarts';
	export default {
		props: {
			height: {
				type: String,
				default: "100%"
			},
			width: {
				type: String,
				default: "100%"
			},
			options: {
				type: Object,
				default: null
			}
		},
		date() {
			return {
				uuid: null,
				myChart: null,
			}
		},
		watch: {
		  options: {
		    handler (val, oldV) {

				if(val!=null){
					// 基于准备好的dom，初始化echarts实例
					this.myChart = echarts.init(document.getElementById(this.uuid));
					// 绘制图表
					this.myChart.setOption(this.options);
				}

		    },
		    deep: true
		    // immediate: false
		  }
		},
		computed: {
			style() {
				return {
					width: this.width,
					height: this.height,
				}
			}
		},
		created() {
			this.uuid = this.getUuid();
		},
		mounted() {

			if(this.options!=null){
				this.myChart = echarts.init(document.getElementById(this.uuid));
				// 绘制图表
				this.myChart.setOption(this.options);

				window.onresize = () => {
					return (() => {
						this.myChart.resize();
					})()
				}
			}

		},
		methods:{
			getUuid(){
        return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
            (c ^ window.crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
        );
			}
		}
	}
</script>
<style scoped>
</style>
