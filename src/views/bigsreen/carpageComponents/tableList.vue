<template>
  <div class="data_main">
    <table class="Realtime_alarm_table">
      <thead>
        <tr>
          <td v-for="(col, index) in headerColumns" :key="index" :style="{ width: col.widthPx }">{{ col.label }}</td>
        </tr>
      </thead>
    </table>
    <div class="data_com">
      <table class="Realtime_alarm_table">
        <vue3-seamless-scroll :list="list" v-model="show" :step="0.2">
          <template v-slot="{ data }">
            <tbody>
              <tr v-for="(item, index) in areaData" :key="index">
                <td v-for="(col, colIndex) in headerColumns" :key="colIndex" :style="{ width: col.widthPx }">
                  {{ item[col.key] }}
                </td>
              </tr>
            </tbody>
          </template>
        </vue3-seamless-scroll>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      required: true,
    },
  },
  watch: {
    tableData: {
      handler(newVal) {
        this.areaData = newVal;
      },
      immediate: true,
    },
  },
  data() {
    return {
      list: [
        { name: '1' },
        { name: '2' },
        { name: '3' }
      ],
      headerColumns: [
        { label: '车型', key: 'modelName', widthPercent: 7 },
        { label: '品牌', key: 'brandName', widthPercent: 7 },
        { label: 'VIN', key: 'vin', widthPercent: 20 },
        { label: '零部件', key: 'attackedDevice', widthPercent: 7 },
        { label: '攻击类型', key: 'ruleName', widthPercent: 10 },
        { label: '车辆位置', key: 'attackDestArea', widthPercent: 10 },
        { label: '攻击位置', key: 'attackSourceArea', widthPercent: 10 },
        { label: '威胁等级', key: 'threatLevel', widthPercent: 9 },
        { label: '时间', key: 'createdTime', widthPercent: 20 }
      ],
      classOption: {
        step: 0.5
      }
    };
  },
  mounted() {
    this.updateColumnWidths();
    window.addEventListener('resize', this.updateColumnWidths);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateColumnWidths);
  },
  methods: {
    updateColumnWidths() {
      const screenWidth = window.innerWidth;
      this.headerColumns.forEach(col => {
        col.widthPx = `${(screenWidth * col.widthPercent) / 100}px`;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.data_main {
  width: 100%;
}

.Realtime_alarm_table {
  width: 100%;
  font-size: 14px;
  font-family: Microsoft YaHei;
  border-collapse: collapse;
  text-align: center;

  thead {
    td {
      padding: 0;
      font-family: MicrosoftYaHei, MicrosoftYaHei;
      font-weight: bold;
      font-size: 14px;
      color: #9ea4b6;
      text-align: left;
      font-style: normal;
      padding: 9px 0px 9px 18px;
      box-sizing: border-box;
      border-bottom: 1px solid #40688e;
    }
  }

  tbody {
    tr {
      border-bottom: 1px dashed rgba(105, 175, 255, 0.37);

      &:nth-child(odd) {
        background: rgba(27, 130, 183, .32);
      }

      &:nth-child(even) {
        background: #0f2643;
      }
    }

    td {
      height: auto;
      min-height: 30px;
      padding: 20px 0px 20px 18px;
      font-family: MicrosoftYaHei;
      font-size: 14px;
      line-height: 20px;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      box-sizing: border-box;
      word-wrap: break-word;
      overflow-wrap: break-word;
      word-break: break-all;
      border-bottom: 1px solid #1B82B7;

    }
  }

  .align_cen {
    text-align: center;
  }

  .tnum {
    width: 22px;
    height: 17px;
    line-height: 15px;
    background: rgba(23, 131, 255, 0.2);
    box-shadow: inset 0px 0 6px 0px #17F4FF;
    border-radius: 3px;
    border: 1px solid #1D70DD;
    font-family: DingTalk JinBuTi, DingTalk JinBuTi;
    font-weight: 400;
    font-size: 10px;
    color: #48ADFF;
    text-align: center;
    font-style: normal;
    text-transform: none;
    display: inline-block;
  }
}

.data_com {
  height: 256px;
  overflow: hidden;
}

.scroll-wrap {
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.Realtime_alarm_table tbody {
  min-height: 300px;
  /* 大于容器高度 */
}
</style>
