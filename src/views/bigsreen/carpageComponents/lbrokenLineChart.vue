<template>
  <div>
    <MyEcharts width="100%" height="245px" :options="options1"></MyEcharts>
  </div>
</template>

<script>
import MyEcharts from './MyEcharts.vue';
import * as echarts from 'echarts';
export default {
  data() {
    return {
      options1: null,
    };
  },
  components: {
    MyEcharts,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        this.options1 = this.getPiechartOption();
      },
      deep: true,
    },
  },
  mounted() {
    this.options1 = this.getPiechartOption();
  },
  methods: {
    getPiechartOption() {
      var fontColor = '#30eee9';
      let option = {
        grid: {
          left: '5%',
          right: '5%',
          top: '25%',
          bottom: '5%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          borderColor: 'rgba(0, 0, 0, 0.7)',
          textStyle: {
            color: '#fff',
          },
          trigger: 'axis',
        },
        legend: {
          show: true,
          x: '15', // 图例左对齐
          y: '0',
          icon: '', // 图例的形状 有哪些参数
          itemWidth: 15,
          itemHeight: 10,
          textStyle: {
            color: '#A5CDFA', // 图例文字颜色为 #A5CDFA
            fontSize: 12, // 设置图例文字大小为 12px
          },
          data: this.data.legendData, // 添加新的黄色线条图例
          selectedMode: false,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: 1,
            axisLine: {
              show: false,
            },
            axisLabel: {
              color: '#A5CDFA', // 设置 X 轴标签颜色为 #A5CDFA
              fontSize: 12, // 设置 X 轴标签字体大小为 12px
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            data: this.data.xAxisData,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位           ',
            nameTextStyle: {
              color: '#C7E1FF',
              fontSize: 12,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255,255,255,0.1)',
                type: 'dashed',
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              margin: 10,
              textStyle: {
                color: '#C7E1FF', // 设置 Y 轴标签颜色为 #C7E1FF
                fontSize: 12, // 设置 Y 轴标签字体大小为 12px
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: this.data.legendData[0],
            type: 'line',
            smooth: true,
            stack: '总量',
            symbolSize: 0,
            itemStyle: {
              normal: {
                color: '#4293FD',
                lineStyle: {
                  color: '#4293FD',
                  width: 2,
                },
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(19, 95, 172, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(112, 154, 195, 0.1)',
                    },
                  ],
                  false
                ),
              },
            },
            data: this.data.seriesData[0],
          },
          {
            name: this.data.legendData[1],
            type: 'line',
            smooth: true,
            stack: '总量',
            symbolSize: 0,
            itemStyle: {
              normal: {
                color: '#23D0C4',
                lineStyle: {
                  color: '#23D0C4',
                  width: 2,
                },
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(50, 216, 205, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(35, 208, 196, 0.1)',
                    },
                  ],
                  false
                ),
              },
            },
            data: this.data.seriesData[1],
          },
          {
            name: this.data.legendData[2],
            type: 'line',
            smooth: true,
            stack: '总量',
            symbolSize: 0,
            itemStyle: {
              normal: {
                color: '#FFEB3B', // 设置黄色线条的颜色
                lineStyle: {
                  color: '#FFEB3B',
                  width: 2,
                },
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(255, 235, 59, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 235, 59, 0.1)',
                    },
                  ],
                  false
                ),
              },
            },
            data: this.data.seriesData[2], // 设置黄色线条的示例数据
          },
        ],
      };

      return option;
    },
  },
};
</script>

<style lang="less" scoped></style>
