<template>
  <div class="carpage_layout">
    <countBar class="count_box" :car-type="carType"></countBar>
    <div class="info_header">
      <div class="header_select">
        <div style="width: 138px; position: relative">
          <Select class="big-select" placeholder="请选择" v-model="carType">
            <Option v-for="item in carModelOptions" :key="item.id" :value="item.id">
              {{ item.modelName }}
            </Option>
          </Select>
        </div>
<!--        <div style="width: 138px; position: relative">-->
<!--          <Select class="big-select" placeholder="请输入">-->
<!--            <Option value="1">最近90天</Option>-->
<!--            <Option value="2">最近30天</Option>-->
<!--            <Option value="3">最近10天</Option>-->
<!--          </Select>-->
<!--        </div>-->
      </div>
    </div>
    <div class="info_content">
      <div class="column_one">
        <div class="left_column">
          <div class="column_box">
            <div class="column_title titlebg1">
              <div class="tittext">项目车辆分布</div>
            </div>
            <div class="column_content">
              <div class="yearechart_box">
                <circularTimeEchart :data="yearEchartData1"></circularTimeEchart>
              </div>
            </div>
          </div>
          <div class="column_box">
            <div class="column_title titlebg1">
              <div class="tittext">项目事件分布</div>
            </div>
            <div class="column_content">
              <div class="yearechart_box">
                <circularTimeEchart :data="yearEchartData2"></circularTimeEchart>
              </div>
            </div>
          </div>
        </div>
        <div class="column_box" style="width: 427px">
          <div class="column_title titlebg2">
            <div class="tittext">零部件趋势</div>
          </div>
          <div class="column_content lbrokenlinechart" style="padding-top: 15px;">
            <lbrokenLineChart :data="lbrokenLineChartData"></lbrokenLineChart>
<!--            <div class="selectcustomized_box">-->
<!--                <Select class="big-select selectcustomized" placeholder="请输入">-->
<!--                    <Option value="1">1/2</Option>-->
<!--                    <Option value="2">1/3</Option>-->
<!--                </Select>-->
<!--            </div>-->
        </div>
        </div>
      </div>
      <div class="column_two">
        <div class="column_box">
          <div class="column_title titlebg3">
            <div class="tabitem" :class="{ active: isActive == 1 }" @click="changeTab(1)">
              实时监控
            </div>
<!--            <div class="tabitem" :class="{ active: isActive == 2 }" @click="changeTab(2)">-->
<!--              攻击类型分布-->
<!--            </div>-->
          </div>
          <div class="column_content">
            <div class="chart_column">
              <div class="chart_item">
                <circuaarEchart :data="circularData1"></circuaarEchart>
              </div>
              <div class="chart_item">
                <circuaarEchart :data="circularData2"></circuaarEchart>
              </div>
              <div class="chart_item">
                <circuaarEchart :data="circularData3"></circuaarEchart>
              </div>
              <div class="chart_item">
                <circuaarEchart :data="circularData4"></circuaarEchart>
              </div>
              <div class="chart_item">
                <circuaarEchart :data="circularData5"></circuaarEchart>
              </div>
              <div class="chart_item">
                <circuaarEchart :data="circularData6"></circuaarEchart>
              </div>
              <div class="chart_item">
                <circuaarEchart :data="circularData7"></circuaarEchart>
              </div>
              <div class="chart_item">
                <circuaarEchart :data="circularData8"></circuaarEchart>
              </div>
            </div>
            <div class="table_column">
              <tableList :tableData="tableData"></tableList>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="center_carlayout">
      <div class="car_img">
        <img class="img" src="~@/assets/images/bigScreenImg/carpic.png" alt="" />
        <div class="gw absolute_text">
          <span class="tit">GW：</span>
          <span>{{carPartAttackData.gw}}</span>
          <br />
          <span class="tit">GW-M：</span>
          <span>{{carPartAttackData.gwM}}</span>
        </div>
        <div class="tbox absolute_text">
          <span class="tit">Tbox：</span>
          <span>{{carPartAttackData.tBox}}</span>
        </div>
        <div class="cdca absolute_text">
          <span class="tit">CDC-A：</span>
          <span>{{carPartAttackData.cdcA}}</span>
        </div>
        <div class="M absolute_text">
          <span class="tit">IVI：</span>
          <span>{{carPartAttackData.ivi}}</span>
        </div>
        <div class="SAF absolute_text">
          <span class="tit">SAF：</span>
          <span>{{carPartAttackData.saf}}</span>
        </div>
        <div class="tbox5g absolute_text">
          <span class="tit">Tbox-5G：</span>
          <span>{{carPartAttackData.tBoxFG}}</span>
        </div>
        <div class="VDF absolute_text">
          <span class="tit">VDF：</span>
          <span>{{carPartAttackData.vdf}}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import countBar from './components/countBar.vue';
import circuaarEchart from '~@/views/bigsreen/carpageComponents/circularEchart.vue';
import circularTimeEchart from '~@/views/bigsreen/carpageComponents/circularTimeEchart.vue';
import tableList from '~@/views/bigsreen/carpageComponents/tableList.vue';
import lbrokenLineChart from '~@/views/bigsreen/carpageComponents/lbrokenLineChart.vue';

import { getAttackListData, getCarPartAttackData, getXmclfbData, getXmsjfbData, getLbjqsLineData, getWxdjfbData, getCarModelData } from '@/api/bigscreen/bigscreen';

export default {
  name: 'carpage',
  data() {
    return {
      isActive: 1,
      carType: '018e8c4f-4f5a-7c9d-9f8f-1234567890a4',
      carModelOptions: [],
      tableData: [
        {}
        // {
        //   carModel: 'CarM4',
        //   projectNum: 20231,
        //   VINname: 'LSKIDPS0000000001',
        //   Parts: 'tbox',
        //   attackType: 'TCP_SYN端口扫描',
        //   carPosition: '福建福州',
        //   attackLocation: '上海',
        //   threatLevel: '低',
        //   time: '2025-01-12 12:12:23',
        // }
      ],
      carPartAttackData: {
        cdcA: 0,
        gw: 0,
        gwM: 0,
        ivi: 0,
        saf: 0,
        tBox: 0,
        tBoxFG: 0,
        vdf: 0
      },
      circularData1: {
        title: 'CDC-A',
        optionsData: [],
      },
      circularData2: {
        title: 'GW',
        optionsData: [],
      },
      circularData3: {
        title: 'GW-M',
        optionsData: [],
      },
      circularData4: {
        title: 'IVI',
        optionsData: [],
      },
      circularData5: {
        title: 'SAF',
        optionsData: [],
      },
      circularData6: {
        title: 'TBOX',
        optionsData: [],
      },
      circularData7: {
        title: 'TBOX-5G',
        optionsData: [],
      },
      circularData8: {
        title: 'VDF',
        optionsData: [],
      },
      yearEchartData1: {
        title: '车辆数',
        optionsData: [
          // { name: '2025年', value: 20 },
        ],
      },
      yearEchartData2: {
        title: '事件数',
        optionsData: [
          // { name: '2025年', value: 20 }
        ],
      },
      lbrokenLineChartData:{
        legendData: ['CDC-A', 'GW', 'GW-M'],
        xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        seriesData: [
          [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        ],
      }
    };
  },
  components: {
    countBar,
    circuaarEchart,
    circularTimeEchart,
    tableList,
    lbrokenLineChart
  },
  mounted() {
    this.loadCarModelData();
    this.loadAttackListData();
    this.loadCarPartAttackData();
    this.loadXmclfbData();
    this.loadXmsjfbData();
    this.loadLbjqsLineData();
    this.loadWxdjfbData();
  },
  watch: {
    carType: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.loadAttackListData();
          this.loadCarPartAttackData();
          this.loadXmclfbData();
          this.loadXmsjfbData();
          this.loadLbjqsLineData();
          this.loadWxdjfbData();
        }
      },
      immediate: true,
    },
  },
  methods: {
    changeTab(index) {
      this.isActive = index;
    },
    async loadCarModelData() {
      try {
        const self = this;
        const response = await getCarModelData();
        if (response) {
          self.carModelOptions = response;
        }
      } catch (error) {
      }
      return null;
    },
    async loadAttackListData() {
      try {
        const self = this;
        const response = await getAttackListData({carType: self.carType});
        if (response) {
          self.tableData = response;
        }
      } catch (error) {
      }
      return null;
    },
    async loadCarPartAttackData() {
      try {
        const self = this;
        const response = await getCarPartAttackData({carType: self.carType});
        if (response) {
          self.carPartAttackData = response;
        }
      } catch (error) {
      }
    },
    async loadXmclfbData() {
      try {
        const self = this;
        const response = await getXmclfbData({carType: self.carType});
        if (response) {
          self.yearEchartData1.optionsData = response;
        }
      } catch (error) {
      }
    },
    async loadXmsjfbData() {
      try {
        const self = this;
        const response = await getXmsjfbData({carType: self.carType});
        if (response) {
          self.yearEchartData2.optionsData = response;
        }
      } catch (error) {
      }
    },
    async loadLbjqsLineData() {
      try {
        const self = this;
        const response = await getLbjqsLineData({carType: self.carType});
        if (response) {
          self.lbrokenLineChartData.legendData = response.legendData;
          self.lbrokenLineChartData.xAxisData = response.xAxisData;
          self.lbrokenLineChartData.seriesData = response.seriesData;
        }
      } catch (error) {
      }
    },
    async loadWxdjfbData() {
      try {
        const self = this;
        const response = await getWxdjfbData({carType: self.carType});
        if (response) {
          self.circularData1.optionsData = response.cdcA;
          self.circularData2.optionsData = response.gw;
          self.circularData3.optionsData = response.gwM;
          self.circularData4.optionsData = response.ivi;
          self.circularData5.optionsData = response.saf;
          self.circularData6.optionsData = response.tBox;
          self.circularData7.optionsData = response.tBoxF;
          self.circularData8.optionsData = response.vdf;
        }
      } catch (error) {
      }
    },
  },
};
</script>

<style lang="less" scoped>
.count_box {
  position: absolute;
  top: 81px;
  left: 50%;
  transform: translateX(-50%);
}

.carpage_layout::-webkit-scrollbar {
  width: 8px; /* 滚动条宽度 */
}

.carpage_layout::-webkit-scrollbar-track {
  background: rgba(0,0,0,1); /* 轨道的背景颜色 */
  border-radius: 10px; /* 轨道的圆角 */
}

.carpage_layout::-webkit-scrollbar-thumb {
  background-color: #4A90E2; /* 滚动条的颜色 */
  border-radius: 10px; /* 滚动条的圆角 */
  border: none; /* 滚动条的边框，和轨道形成对比 */
}

.carpage_layout::-webkit-scrollbar-thumb:hover {
  background-color: #2a6fbf; /* 鼠标悬停时的滚动条颜色 */
}
.carpage_layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: url('~@/assets/images/bigScreenImg/one_bodybg.png') left top no-repeat;
  background-size: 100% 100%;
  position: relative;
  overflow-y: auto;
  /* 自定义滚动条 */
  scrollbar-width: thin; /* Firefox 滚动条宽度设置为细 */
  scrollbar-color: #4A90E2 #0d1a38; /* 滚动条颜色，前面是滑块，后面是轨道 */

  .info_header {
    width: 1920px;
    height: 127px;
    position: relative;
    background: url('~@/assets/images/bigScreenImg/one_header.png') left top no-repeat;
    .toptitle {
      width: 100%;
      position: absolute;
      mix-blend-mode: overlay;
      left: 0;
      top: 0;
      z-index: 2;
    }

    .header_select {
      position: absolute;
      left: 54px;
      top: 58px;
      display: flex;
      align-items: center;
      gap: 11px;
    }
  }
  .info_content {
    .column_one {
      padding: 0 36px;
      display: flex;
      justify-content: space-between;
      .left_column {
        display: flex;
        gap: 18px;
        .column_box {
          width: 230px;
        }
      }

      .column_content {
        height: 191px;
        padding-top: 53px;
        position: relative;
        background: linear-gradient(
          to bottom,
          rgba(26, 89, 174, 0.5) 0%,
          rgba(26, 89, 174, 0.1) 20%,
          rgba(26, 89, 174, 0) 50%
        );
      }
    }
    .column_two {
      margin-top: 108px;
      padding: 0 36px;
      .column_content{
         background: linear-gradient(
          to bottom,
          rgba(26, 89, 174, 0.5) 0%,
          rgba(26, 89, 174, 0.1) 20%,
          rgba(26, 89, 174, 0) 50%
        );
      }
      .column_title {
        display: flex;
        align-items: center;
        gap: 30px;
        .tabitem {
          margin-top: -12px;
        }
      }
      .chart_column {
        padding-top: 54px;
        display: grid;
        grid-template-columns: repeat(8, 12.5%);
        .chart_item {
          height: 125px;
        }
      }
      .table_column{
        margin-top: 26px;
      }
    }
  }
  .center_carlayout {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 185px;
    z-index: 5;
    width: 685px;
    height: 411px;
    .car_img {
      position: relative;
      width: 685px;
      height: 411px;
      .img {
        width: 685px;
        height: 411px;
      }
      .absolute_text {
        position: absolute;
        font-size: 14px;
        color: #fff;
        line-height: 1.2;
        left: 0;
        top: 0;
      }

      .tit {
        display: inline-block;
        min-width: 53px;
        text-align: right;
      }
      .gw {
        left: 168px;
        top: 68px;
      }
      .tbox {
        left: 245px;
        top: 39px;
      }
      .cdca {
        left: 145px;
        top: 138px;
      }
      .M {
        left: 208px;
        top: 145px;
      }
      .SAF {
        left: 283px;
        top: 153px;
      }
      .tbox5g {
        left: 386px;
        top: 51px;
      }
      .VDF {
        left: 481px;
        top: 36px;
      }
    }
  }
}

.column_title {
  height: 43px;
  padding-left: 40px;
  &.titlebg1 {
    background: url('~@/assets/images/bigScreenImg/titlebg2.png') left center no-repeat;
  }
  &.titlebg2 {
    background: url('~@/assets/images/bigScreenImg/titlebg1.png') left center no-repeat;
  }
  &.titlebg3 {
    background: url('~@/assets/images/bigScreenImg/titlebg3.png') left center no-repeat;
  }

  .tittext {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom, #bfebff 0%, #cef0ff 30%, #ffffff 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.4;
  }

  .tabitem {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    cursor: pointer;
    background: linear-gradient(to bottom, #d1d5dd 0%, #acb6c6 30%, #939aa7 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.25;
    &.active {
      background: linear-gradient(to bottom, #bfebff 0%, #cef0ff 30%, #ffffff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }
}

.selectcustomized_box{
    position: absolute;
    top: 13px;
    right:22px;
}

@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('./fonts/YouSheBiaoTiHei-2.eot');
  /***兼容ie9***/
  src: url('./fonts/YouSheBiaoTiHei-2.eot?#iefix') format('embedded-opentype'),
    /***兼容ie6-ie8***/ url('./fonts/YouSheBiaoTiHei-2.woff') format('woff'), local('DIN'),
    url('./fonts/YouSheBiaoTiHei-2.woff');
}
//下拉选框


/deep/ .big-select {
  .ivu-select-item {
    color: #fff;
    &:hover {
      background: rgba(30, 144, 255, 0.2);
    }
  }
  &.selectcustomized{
    .ivu-select-selection {
        height: 24px;
        background: rgba(0,0,0,0.2);
        border-radius: 3px;
        border: none;
        width: 101px;
        border: 1px solid rgba(255,255,255,0.1);
    }
    .ivu-select-arrow {
        color: #fff;
    }
     .ivu-select-placeholder {
        height: 24px !important;
        line-height: 24px !important;
    }
    .ivu-select-selected-value {
        height: 24px !important;
        line-height: 24px !important;
    }
  }

  .ivu-select-selection {
    height: 36px;
    background: rgba(42, 119, 173, 0.4);
    border-radius: 1px 1px 1px 1px;
    border: none;
    background: url('~@/assets/images/bigScreenImg/select_bg.png') left top no-repeat;
    background-size: 100% 100%;
  }

  .ivu-select-arrow {
    color: #61b8d3;
  }

  .ivu-form-item-content {
    margin-left: 0 !important;
  }

  .ivu-form-item {
    margin-bottom: 0;
  }

  .ivu-select-dropdown {
    background: #142d63;
    border: 1px solid #025789;
    padding: 0;
  }
  &.ivu-select-multiple .ivu-select-item-focus {
    background: #1e90ff !important;
  }


  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #468a9e;
    border-radius: 10px;
  }

  .ivu-select-item-selected,
  .ivu-select-item-selected:hover {
    background: #1e90ff;
  }
   .ivu-select-placeholder {
    height: 36px !important;
    line-height: 36px !important;
    font-size: 14px !important;
  }
  .ivu-select-selected-value {
    height: 36px !important;
    line-height: 36px !important;
    color: #fff;
  }
}
//下拉选框END
</style>
