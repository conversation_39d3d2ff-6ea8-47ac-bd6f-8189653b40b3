<template>
  <div class="digital_layout">
    <div class="info_header">
<!--      <div class="header_select">-->
<!--        <div style="width: 138px; position: relative;">-->
<!--          <Select class="big-select" placeholder="请输入">-->
<!--            <Option value="1">最近90天</Option>-->
<!--            <Option value="2">最近30天</Option>-->
<!--            <Option value="3">最近10天</Option>-->
<!--          </Select>-->
<!--        </div>-->
<!--      </div>-->
    </div>
    <!-- 统计数据 -->
    <countBar class="count_box"></countBar>

    <!-- 左窗口 -->
    <div class="digital_left scroller">
      <wxdjtj></wxdjtj>
      <gjlxtop></gjlxtop>
      <gjqs></gjqs>
    </div>
    <!-- 右窗口 -->
    <div class="digital_right scroller">
      <!-- <happy-scroll></happy-scroll> -->
      <pptj></pptj>
      <bgjph></bgjph>
      <lbjtop></lbjtop>
    </div>
    <!-- 中间窗口 -->
    <div class="digital_mid">
      <div class="mid_top scroller">
        <midMap></midMap>
      </div>
      <!-- 底部区域 -->
      <div class="mid_bottom">
        <carDataList></carDataList>
      </div>
    </div>

  </div>
</template>

<script>
import countBar from './components/countBar.vue'
import carDataList from './components/carDataList.vue'
import gjlxtop from './components/gjlxtop.vue'
import gjqs from './components/gjqs.vue'
import wxdjtj from './components/wxdjtj.vue'
import pptj from './components/pptj.vue'
import bgjph from './components/bgjph.vue'
import lbjtop from './components/lbjtop.vue'
import midMap from './components/midMap.vue'
export default {
  data() {
    return {
      aa: "asd123456"
    }
  },
  components: {
    countBar,
    carDataList,
    gjlxtop,
    gjqs,
    wxdjtj,
    pptj,
    bgjph,
    lbjtop,
    midMap
  },
  methods: {
    chart() {
      const data = {
        a: '1',
        b: '2'
      }
      this.aa = data.a;
    }
  },
  mounted() {
    this.chart();
  }
}
</script>

<style lang="less" scoped>
.digital_layout {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #04142C;
  background: url('~@/assets/images/bigScreenImg/big_bgp2.png') no-repeat center top;
  background-size: 100% 100%;

  .info_header {
    width: 1920px;
    height: 127px;
    position: relative;
    background: url("~@/assets/images/bigScreenImg/one_header.png") left top no-repeat;

    .toptitle {
      width: 100%;
      position: absolute;
      mix-blend-mode: overlay;
      left: 0;
      top: 0;
      z-index: 2;
    }

    .header_select {
      position: absolute;
      left: 54px;
      top: 58px;
      display: flex;
      align-items: center;
      gap: 11px;
    }
  }
}

.count_box {
  position: absolute;
  top: 81px;
  left: 50%;
  transform: translateX(-50%);
}

.map-selectwrap {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  top: 130px;
  z-index: 9;
}

.top_time {
  // font-family: Bebas Neue, Bebas Neue;
  font-weight: 400;
  font-size: 16px;
  color: #D2EDFF;
  line-height: 14px;
  text-align: center;
  font-style: normal;
  position: absolute;
  top: 26px;
  left: 55px;
  display: flex;
  align-items: center;

  .icon_time {
    margin-right: 10px;
  }
}

.open_map {
  position: absolute;
  top: 26px;
  right: 55px;
  cursor: pointer;
  font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
  font-weight: bold;
  font-size: 14px;
  color: #FFFFFF;
  letter-spacing: 3px;
  text-shadow: 0px 3px 3px rgba(0, 0, 0, 0.25);
  text-align: center;
  font-style: normal;
  display: flex;
  align-items: center;

  .icon_back {
    width: 25px;
    height: 25px;
    margin-right: 6px;
  }
}

/* 标题样式 - 使用rem单位 */
.large_screen_title {
  height: 4.218rem;
  width: 100%;
  position: absolute;
  //   background: url('~@/assets/images/darkScreenimg/bg_head.png') 34px top no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  position: relative;

  h1 {
    font-family: 'YouSheBiaoTiHei';
    font-weight: bold;
    font-size: 30px;
    padding-top: 4px;
    background-image: linear-gradient(360deg, #77BAFF 0%, #FFFFFF 58%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    font-weight: normal;
    position: relative;

    &::after {
      content: "石狮市智慧水务排水大屏";
      position: absolute;
      z-index: -1;
      left: 50%;
      top: 0;
      text-shadow: 2px 1px 15px rbba(0, 0, 0, 1);
      //往做拉50%
      transform: translateX(-50%);
    }
  }
}

/* 背景元素 - 使用百分比定位 */
.lef_bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 36px;
  height: 100vh;
  //   background: url('~@/assets/images/darkScreenimg/bg_lef.png') center 17px no-repeat;
  background-size: 100% 99%;
  z-index: 1;
}

.rig_bg {
  position: absolute;
  right: 0;
  top: 0;
  width: 36px;
  height: 100vh;
  //   background: url('~@/assets/images/darkScreenimg/bg_rig.png') center 17px no-repeat;
  background-size: 100% 99%;
  z-index: 1;
}

.bottom_bg {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 5.55vh;
  //   background: url('~@/assets/images/bigScreenimg/bg_bot.png') center center no-repeat;
  background-size: 88% 100%;
  z-index: 1;
}

.arr_lt {
  position: absolute;
  top: 10px;
  left: 0;
}

.arr_rt {
  position: absolute;
  top: 10px;
  right: 0;
}

.arr_lb {
  position: absolute;
  bottom: 0;
  left: 0;
}

.arr_rb {
  position: absolute;
  bottom: 0;
  right: 0;
}

.arr_p {
  width: 35px;
  height: 36px;
  z-index: 20;
}

/* 左右面板 - 使用rem单位 */
.digital_left {
  position: absolute;
  top: 130px;
  left: 32px;
  bottom: 70px;
  // width: 26rem;
  width: 22%;
  z-index: 7;
  transition: all 0.3s;
  height: calc(~'100vh - 141px');
  overflow-y: auto;
  overflow-x: hidden;

  &.scroller::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &.scroller::-webkit-scrollbar-track {
    background-color: #4a90e2;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }

  &.scroller::-webkit-scrollbar-thumb {
    background-color: transparent;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }

  /* 如果不需要交互 */

  &.extended {
    left: -26rem;
    /* 收起 */
  }
}

.digital_right {
  position: absolute;
  top: 130px;
  right: 32px;
  bottom: 70px;
  width: 22%;
  z-index: 7;
  transition: all 0.3s;
  height: calc(~'100vh - 141px');
  overflow-y: auto;
  overflow-x: hidden;
  &.scroller::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &.scroller::-webkit-scrollbar-track {
    background-color: #4a90e2;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }

  &.scroller::-webkit-scrollbar-thumb {
    background-color: transparent;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }

  /* 如果不需要交互 */

  &.extended {
    right: -26rem;
    /* 收起 */
  }

  .screen_fullicon {
    position: absolute;
    top: 28px;
    left: -76px;
    z-index: 30;
    width: 54px;
    height: 56px;
    cursor: pointer;
    pointer-events: auto;
  }

  .wz_icon {
    position: absolute;
    top: 85px;
    left: -76px;
    z-index: 30;
    width: 54px;
    height: 56px;
    cursor: pointer;
    pointer-events: auto;
  }
}

.digital_mid {
  display: flex;
  // flex-wrap: wrap;
  flex-direction: column;
  width: 50%;
  // border: 1px solid #f00;
  // height: 87%;
  position: absolute;
  top: 145px;
  left: 50%;
  transform: translateX(-50%);
  bottom: 20px;
  z-index: 7;

  .mid_top {
    height: 628px;
    width: 100%;
    overflow: hidden;
    overflow-y: auto;

    // border: 1px solid #f00;
    &.scroller::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }

    &.scroller::-webkit-scrollbar-track {
      background-color: rgba(13, 13, 13, 0);
      -webkit-border-radius: 2em;
      -moz-border-radius: 2em;
      border-radius: 2em;
    }

    &.scroller::-webkit-scrollbar-thumb {
      background-color: transparent;
      -webkit-border-radius: 2em;
      -moz-border-radius: 2em;
      border-radius: 2em;
    }
  }

  .mid_bottom {
    width: 100%;
    //  border: 1px solid #f00;
    display: flex;
    flex: 1;
    justify-content: space-around;

    .content_box {
      width: 50%;
      // border: 1px solid #f00;
    }
  }
}

.search_box {
  position: absolute;
  z-index: 10;
  top: 4.7rem;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
}

:deep(.happy-scroll-container) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.happy-scroll-container .happy-scroll-content) {
  width: 100%;
}

.tcgl {
  position: fixed;
  left: 434px;
  top: 117px;
  z-index: 9;
  pointer-events: auto;
  transition: all 0.3s;

  &.tcglextended {
    left: 24px;
  }
}

/* 确保地图本身可交互 */
#SceneView {
  pointer-events: auto;
  z-index: 19;
}

/* 装饰性元素穿透 */
.lef_bg,
.rig_bg,
.bottom_bg,
.arr_lt,
.arr_rt,
.arr_lb,
.arr_rb,
.large_screen_title {

  z-index: 1;
}

#main_mapcontent {
  z-index: 19;
  pointer-events: auto;
}

.pointer_true {
  pointer-events: auto;
}

.legend_box {
  position: fixed;
  bottom: 70px;
  right: 441px;
  z-index: 9;
  transition: all 0.3s;

  &.legendextended {
    right: 24px;
  }
}

//下拉选框
/deep/ .big-select {
  .ivu-select-item {
    color: #fff;

    &:hover {
      background: rgba(30, 144, 255, .2);
    }
  }

  .ivu-select-selection {
    height: 36px;
    background: rgba(42, 119, 173, 0.4);
    border-radius: 1px 1px 1px 1px;
    border: none;
    background: url("~@/assets/images/bigScreenImg/select_bg.png") left top no-repeat;
    background-size: 100% 100%;
  }

  .ivu-select-arrow {
    color: #61b8d3;
  }

  .ivu-form-item-content {
    margin-left: 0 !important;
  }

  .ivu-form-item {
    margin-bottom: 0;
  }

  .ivu-select-dropdown {
    background: #142d63;
    border: 1px solid #025789;
    padding: 0;
  }

  &.ivu-select-multiple .ivu-select-item-focus {
    background: #1e90ff !important;
  }

  .ivu-select-placeholder {
    height: 36px !important;
    line-height: 36px !important;
    font-size: 14px !important;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, .1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #468a9e;
    border-radius: 10px;
  }

  .ivu-select-item-selected,
  .ivu-select-item-selected:hover {
    background: #1e90ff;
  }

  .ivu-select-selected-value {
    height: 36px !important;
    line-height: 36px !important;
    color: #fff;
  }
}

//下拉选框END

@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("./fonts/YouSheBiaoTiHei-2.eot");
  /***兼容ie9***/
  src: url("./fonts/YouSheBiaoTiHei-2.eot?#iefix") format("embedded-opentype"),
    /***兼容ie6-ie8***/
    url("./fonts/YouSheBiaoTiHei-2.woff") format("woff"),
    local("DIN"), url("./fonts/YouSheBiaoTiHei-2.woff");
}
</style>
