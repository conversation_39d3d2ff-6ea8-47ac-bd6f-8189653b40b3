<template>
  <div class="column_box">
    <div class="column_title titlebg4">
      <div class="tittext">攻击趋势（次）</div>
    </div>
    <div class="column_content">
      <div style="width: 100%; height: 230px; margin: 0 auto; overflow: hidden;">
        <lbrokenLineChart :data="lbrokenLineChartData"></lbrokenLineChart>
      </div>
    </div>
  </div>

</template>

<script>
import lbrokenLineChart from '~@/views/bigsreen/carpageComponents/lbrokenLineChart.vue';
import { getGjqsLineData } from '@/api/bigscreen/bigscreen';

export default {
  data() {
    return {
      lbrokenLineChartData:{
        legendData: ['ALL'],
        xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        seriesData: [
          [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        ],
      }
    };
  },
  created() {
  },
  mounted() {
    this.loadData();
  },
  components: {
   lbrokenLineChart
  },
  methods: {
    async loadData() {
      try {
        const self = this;
        const response = await getGjqsLineData();
        if (response) {
          self.lbrokenLineChartData.legendData = response.legendData;
          self.lbrokenLineChartData.xAxisData = response.xAxisData;
          self.lbrokenLineChartData.seriesData = response.seriesData;
        }
      } catch (error) {
      }
      return null;
    }
  },

}
</script>

<style lang="less" scoped>
.ranking_box {
  margin-top: 15px;

  .ranking_con {
    width: 100%;
    height: 242px;
    // background: linear-gradient(to right, #F8FAFB, #1A59AE);
    box-sizing: border-box;
  }

}
.column_box {
  width: 100%;
  margin-top:40px;
}
.column_title {
  height: 43px;
  padding-left: 40px;
  background-size: 100% 100% !important;

  &.titlebg1 {
    background: url("~@/assets/images/bigScreenImg/titlebg2.png") left center no-repeat;
  }

  &.titlebg2 {
    background: url("~@/assets/images/bigScreenImg/titlebg1.png") left center no-repeat;
  }

  &.titlebg3 {
    background: url("~@/assets/images/bigScreenImg/titlebg3.png") left center no-repeat;
  }

  &.titlebg4 {
    background: url("~@/assets/images/bigScreenImg/titlebg4.png") left center no-repeat;
  }

  .tittext {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom,
        #bfebff 0%,
        #cef0ff 30%,
        #ffffff 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.4;
  }

  .tabitem {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom,
        #d1d5dd 0%,
        #acb6c6 30%,
        #939aa7 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.25;

    &.active {
      background: linear-gradient(to bottom,
          #bfebff 0%,
          #cef0ff 30%,
          #ffffff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }

}

.column_content {
  height: 242px;
  padding-top: 3px;
  background: linear-gradient(to bottom,
      rgba(26, 89, 174, 0.5) 0%,
      rgba(26, 89, 174, 0.1) 20%,
      rgba(26, 89, 174, 0) 50%);
  box-sizing: border-box;
}

.column_box {
  width: 100%;
}
</style>
