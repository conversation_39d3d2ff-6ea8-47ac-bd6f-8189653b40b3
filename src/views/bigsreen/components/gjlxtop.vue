<template>
  <div class="column_box">
    <div class="column_title titlebg4">
      <div class="tittext">攻击类型排行TOP 5</div>
    </div>
    <div class="column_content">
      <div class="rankList">
        <!-- 动态渲染数据 -->
        <div
          class="rank-warp"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <div class="rank-label">{{ item.name }}</div>
          <div class="rank-progress">
            <div
              class="rank-value"
              :style="{ width: calculateWidth(item.data) + '%' }"
            >
              {{ item.data }}
            </div>
            <div
              class="rank-rate"
              :style="{ width: calculateWidth(item.data) + '%' }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getGjlxTopFiveData } from '@/api/bigscreen/bigscreen';

export default {
  data() {
    return {
      dataList: [
        // { name: 'TCP_SYN端口', data: 34939 },
        // { name: 'Non_spoofe', data: 16219 },
        // { name: 'ICMP Echo..', data: 11997 },
        // { name: 'Telnet密码破解', data: 9029 },
        // { name: '量负载率检测', data: 5422 }
      ],
      maxValue: 1 // 数据中的最大值（用于计算百分比）
    }
  },
  created() {
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 计算进度条宽度百分比
    calculateWidth(value) {
      return Math.round((value / this.maxValue) * 100);
    },
    async loadData() {
      try {
        const self = this;
        const response = await getGjlxTopFiveData();
        if (response) {
          self.maxValue = response.reduce((max, item) => (item.data > max ? item.data : max), 0);
          self.dataList = response;
        }
      } catch (error) { /* empty */ }
      return null;
    }
  },
}
</script>

<style lang="less" scoped>
.ranking_box {
  margin-top: 15px;

  .ranking_con {
    width: 100%;
    height: 242px;
    // background: linear-gradient(to right, #F8FAFB, #1A59AE);
    box-sizing: border-box;
  }

}

.rankList {
  padding: 15px 13px;
  width: 100%;
  box-sizing: border-box;
}

.rankList .rank-warp {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 18px;
  align-items: flex-end;
}

.rankList .rank-warp:first-child {
  margin-top: 0px;
}

.rankList .rank-warp .rank-number {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin-right: 7px;
}

.rankList .rank-warp .rank-number .icon {
  width: 16px;
  height: 19px;
}

.rankList .rank-warp .rank-number .number {
  width: 17px;
  height: 17px;
  line-height: 17px;
  text-align: center;
  border: 1px solid #B3D6E6;
  border-radius: 50%;
  font-size: 12px;
  font-family: AlibabaPuHuiTiL;
  color: #B3D6E6;
}

.rankList .rank-warp .rank-label {
  width: 90px;
  min-width: 90px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 400;
  color: #fff;
}

.rankList .rank-warp .rank-progress {
  width: 90%;
  box-sizing: border-box;
  padding: 0 11px 0 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.rankList .rank-warp .rank-progress .rank-rate {
  height: 9px;
  background: linear-gradient(to right, #027fff 0%, #01ffff 100%);
  border-radius: 9px;
}

.rankList .rank-warp .rank-value {
  flex: 1;
  font-size: 14px;
  font-family: NotoSansHans, NotoSansHans;
  font-weight: 400;
  color: #FFFFFF;
  text-align: center;
}

.rankList .rank-warp .rank-value .unit {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 11px;
  color: #FFFFFF;
  line-height: 1;
  margin-left: 6px;
}

.column_title {
  height: 43px;
  padding-left: 40px;
  background-size: 100% 100% !important;

  &.titlebg1 {
    background: url("~@/assets/images/bigScreenImg/titlebg2.png") left center no-repeat;
  }

  &.titlebg2 {
    background: url("~@/assets/images/bigScreenImg/titlebg1.png") left center no-repeat;
  }

  &.titlebg3 {
    background: url("~@/assets/images/bigScreenImg/titlebg3.png") left center no-repeat;
  }

  &.titlebg4 {
    background: url("~@/assets/images/bigScreenImg/titlebg4.png") left center no-repeat;
  }

  .tittext {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom,
        #bfebff 0%,
        #cef0ff 30%,
        #ffffff 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.4;
  }

  .tabitem {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom,
        #d1d5dd 0%,
        #acb6c6 30%,
        #939aa7 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.25;

    &.active {
      background: linear-gradient(to bottom,
          #bfebff 0%,
          #cef0ff 30%,
          #ffffff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }

}

.column_content {
  height: 242px;
  padding-top: 3px;
  background: linear-gradient(to bottom,
      rgba(26, 89, 174, 0.5) 0%,
      rgba(26, 89, 174, 0.1) 20%,
      rgba(26, 89, 174, 0) 50%);
  box-sizing: border-box;
}

.column_box {
  width: 100%;
  margin-top:40px;
}
</style>
