<template>
  <div class="count_bar">
    <div class="count_c">
      <div class="count_title">车辆总数</div>
      <div class="count_data">{{carTotal}}</div>
    </div>
    <div class="count_c">
      <div class="count_title">累计攻击次数</div>
      <div class="count_data">{{attackTotal}}</div>
    </div>
    <div class="count_c">
      <div class="count_title">今日活跃车辆</div>
      <div class="count_data">{{activeCarToDay}}</div>
    </div>
    <div class="count_c">
      <div class="count_title">今日攻击次数</div>
      <div class="count_data">{{attackTotalToDay}}</div>
    </div>
  </div>
</template>

<script>
import { getStatisticData } from '@/api/bigscreen/bigscreen';

export default {
  props: {
    carType: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      carTotal: 0,
      attackTotal: 0,
      activeCarToDay: 0,
      attackTotalToDay: 0
    }
  },
  created() {
    // this.loadData();
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      try {
        const self = this;
        const response = await getStatisticData({ carType: self.carType });
        if (response) {
          self.carTotal = response.carTotal;
          self.activeCarToDay = response.activeCarToDay;
          self.attackTotal = response.attackTotal;
          self.attackTotalToDay = response.attackTotalToDay;
        }
      } catch (error) {
      }
      return null;
    }
  }
}
</script>

<style lang="less" scoped>
.count_bar {
  width: 819px;
  height: 78px;
  background: url('~@/assets/images/bigScreenImg/count_bg.png') no-repeat center top;
  background-size: 100% 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding: 0 94px;

  // gap: 94px;
  .count_c {

    .count_title {
      font-family: MicrosoftYaHei, MicrosoftYaHei;
      font-weight: bold;
      font-size: 16px;
      color: #3FD0FF;
      line-height: 18px;
      text-shadow: 2px 5px 0px #111416;
      text-align: center;
      font-style: normal;
      text-transform: none;
      font-style: italic;
    }

    .count_data {
      font-family: YouSheBiaoTiHei;
      font-size: 24px;
      color: #E4EDF7;
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(180deg, #9CEBFF 0%, #FFFFFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }
}
</style>
