<template>
  <div class="column_box">
    <div class="column_title titlebg4">
      <div class="tittext">威胁等级统计</div>
    </div>
    <div class="column_content">
      <div class="chart_box">
        <circuaarEchart :data="circularData1"></circuaarEchart>
      </div>

    </div>
  </div>

</template>

<script>
import circuaarEchart from '~@/views/bigsreen/carpageComponents/circularEchart.vue';
import { getWxdjtjData } from '@/api/bigscreen/bigscreen';

export default {
  data() {
    return {
      circularData1: {
        title: "安全事件",
        optionsData: [
          { name: '高级', value: 0 },
          { name: '中级', value: 0 },
          { name: '低级', value: 0 },
        ]
      },
    }
  },
  created() {
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      try {
        const self = this;
        const response = await getWxdjtjData();
        if (response) {
          self.circularData1.optionsData = response;
        }
      } catch (error) {
      }
      return null;
    }
  },
  components: {
    circuaarEchart
  },
}
</script>

<style lang="less" scoped>
.ranking_box {
  margin-top: 15px;

  .ranking_con {
    width: 100%;
    height: 242px;
    // background: linear-gradient(to right, #F8FAFB, #1A59AE);
    box-sizing: border-box;
  }

}

.column_title {
  height: 43px;
  padding-left: 40px;
  background-size: 100% 100% !important;

  &.titlebg1 {
    background: url("~@/assets/images/bigScreenImg/titlebg2.png") left center no-repeat;
  }

  &.titlebg2 {
    background: url("~@/assets/images/bigScreenImg/titlebg1.png") left center no-repeat;
  }

  &.titlebg3 {
    background: url("~@/assets/images/bigScreenImg/titlebg3.png") left center no-repeat;
  }

  &.titlebg4 {
    background: url("~@/assets/images/bigScreenImg/titlebg4.png") left center no-repeat;
  }

  .tittext {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom,
        #bfebff 0%,
        #cef0ff 30%,
        #ffffff 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.4;
  }

  .tabitem {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom,
        #d1d5dd 0%,
        #acb6c6 30%,
        #939aa7 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.25;

    &.active {
      background: linear-gradient(to bottom,
          #bfebff 0%,
          #cef0ff 30%,
          #ffffff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }

}

.column_content {
  height: 215px;
  padding-top: 3px;
  background: linear-gradient(to bottom,
      rgba(26, 89, 174, 0.5) 0%,
      rgba(26, 89, 174, 0.1) 20%,
      rgba(26, 89, 174, 0) 50%);
  box-sizing: border-box;

  .chart_box {
    margin-left: -10px;
    margin-top: 20px;
  }
}

.column_box {
  width: 100%;
}
</style>
