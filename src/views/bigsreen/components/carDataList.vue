<template>
  <div class="data_main">
    <table class="Realtime_alarm_table">
      <thead>
        <tr>
          <td style="width:7%;">车型</td>
          <td style="width:7%;">品牌</td>
          <td style="width:20%;">VIN</td>
          <td style="width:7%;">零部件</td>
          <td style="width:10%;">攻击类型</td>
          <td style="width:10%;">车辆位置</td>
          <td style="width:10%;">攻击位置</td>
          <td style="width:9%;">威胁等级</td>
          <td style="width:20%;">时间</td>
        </tr>
      </thead>
    </table>
    <div class="data_com">
      <table class="Realtime_alarm_table">
        <vue3-seamless-scroll :list="areaData" v-model="show" :step="0.2">
          <template v-slot="{ data }">
            <tbody>
              <tr v-for="(item, index) in areaData" :key="index">
                <td style="width:7%;">
                  {{ item.modelName }}
                </td>
                <td style="width:7%;">
                  {{ item.brandName }}
                </td>
                <td style="width:20%;">
                  {{ item.vin }}
                </td>
                <td style="width:7%;">
                  {{ item.attackedDevice }}
                </td>
                <td style="width:10%;">
                  {{ item.ruleName }}
                </td>

                <td style="width:10%;">
                  {{ item.attackDestArea }}
                </td>
                <td style="width:10%;">
                  {{ item.attackSourceArea }}
                </td>
                <td style="width:9%;">
                  {{ item.threatLevel }}
                </td>
                <td style="width:20%;">
                  {{ item.createdTime }}
                </td>
              </tr>
            </tbody>

          </template>

        </vue3-seamless-scroll>
      </table>
    </div>
  </div>
</template>

<script>

import { getAttackListData } from '@/api/bigscreen/bigscreen';


export default {
  components: {
  },
  data() {
    return {
      areaData: [
        {}
        // {
        //   modelName: 'CarM4',
        //   brandName: 2024,
        //   vin: 'LSKIDPS0000000001',
        //   attackedDevice: 'tbox',
        //   ruleName: 'TCP_SYN端口扫描',
        //   attackDestArea: '福建 福州',
        //   attackSourceArea: '上海',
        //   threatLevel: '低',
        //   createdTime: '2025-01-12 12:12:23'
        // }
      ],
      classOption: {
        step: 0.5
      },
    }
  },
  created() {
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      try {
        const self = this;
        const response = await getAttackListData();
        if (response) {
          console.log(response);
          console.log(self.areaData);
          // self.areaData = JSON.parse(JSON.stringify(response));
          self.areaData = [...response];
          console.log(self.areaData);
        }
      } catch (error) {
      }
      return null;
    }
  },
}
</script>

<style lang="less" scoped>
.data_main {
  width: 100%;
}

.Realtime_alarm_table {
  width: 100%;
  font-size: 14px;
  font-family: Microsoft YaHei;
  border-collapse: collapse;
  text-align: center;

  thead {
    td {
      // height: 30px;
      // line-height: 30px;
      padding: 0;
      font-family: MicrosoftYaHei, MicrosoftYaHei;
      font-weight: bold;
      font-size: 14px;
      color: rgba(255, 255, 255, .5);
      text-align: left;
      font-style: normal;
      padding: 9px 0px 9px 18px;
      box-sizing: border-box;
      border-bottom: 1px solid #40688e;
    }
  }

  tbody {
    tr {
      border-bottom: 1px dashed rgba(105, 175, 255, 0.37);

      &:nth-child(odd) {
        background: rgba(27, 130, 183, .32);
      }

      &:nth-child(even) {
        background: rgba(27, 130, 183, .12);
      }
    }

    td {
      height: auto;
      min-height: 30px;
      padding: 20px 0px 20px 18px;
      font-family: MicrosoftYaHei;
      font-size: 14px;
      line-height: 20px;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      box-sizing: border-box;
      word-wrap: break-word;
      overflow-wrap: break-word;
      word-break: break-all;
      border-bottom: 1px solid #1B82B7;

    }
  }

  .align_cen {
    text-align: center;
  }

  .tnum {
    width: 22px;
    height: 17px;
    line-height: 15px;
    background: rgba(23, 131, 255, 0.2);
    box-shadow: inset 0px 0 6px 0px #17F4FF;
    border-radius: 3px;
    border: 1px solid #1D70DD;
    font-family: DingTalk JinBuTi, DingTalk JinBuTi;
    font-weight: 400;
    font-size: 10px;
    color: #48ADFF;
    text-align: center;
    font-style: normal;
    text-transform: none;
    display: inline-block;
  }
}

.data_com {
  height: 256px;
  overflow: hidden;
}

.scroll-wrap {
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.Realtime_alarm_table tbody {
  min-height: 300px;
  /* 大于容器高度 */
}
</style>
