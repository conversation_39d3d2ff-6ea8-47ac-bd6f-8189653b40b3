<template>
  <div class="column_box">
    <div class="column_title titlebg4">
      <div class="tittext">被攻击排行TOP 5</div>
    </div>
    <div class="column_content">
      <div style="width: 100%; height: 230px; margin: 0 auto; overflow: hidden;">
        <MyEcharts width="100%" height="230px" :options="options1" style=" margin-top:10px;"></MyEcharts>
      </div>
    </div>
  </div>

</template>

<script>
import MyEcharts from './MyEcharts.vue'
import * as echarts from 'echarts';

import { getBgjsfTopFiveData } from '@/api/bigscreen/bigscreen';

export default {
  data() {
    return {
      options1: null,
      barData: {
        yData: [],
        xData: []
      }
    };
  },
  components: {
    MyEcharts,
  },
  mounted() {
    this.options1 = this.getPiechartOption(this.barData);
    this.loadData();
  },
  methods: {
    async loadData() {
      try {
        const self = this;
        const response = await getBgjsfTopFiveData();
        if (response) {
          self.barData.yData = response.yData;
          self.barData.xData = response.xData;
          self.options1 = self.getPiechartOption(self.barData);
        }
      } catch (error) {
      }
      return null;
    },
    getPiechartOption(barDataObj) {
      const offsetX = 8;
      const offsetY = 4;
      // 绘制左侧面
      const CubeLeft = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          // 会canvas的应该都能看得懂，shape是从custom传入的
          const xAxisPoint = shape.xAxisPoint;
          // console.log(shape);
          const c0 = [shape.x, shape.y];
          const c1 = [shape.x - offsetX, shape.y - offsetY];
          const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY];
          const c3 = [xAxisPoint[0], xAxisPoint[1]];
          ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath();
        },
      });
      // 绘制右侧面
      const CubeRight = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c1 = [shape.x, shape.y];
          const c2 = [xAxisPoint[0], xAxisPoint[1]];
          const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY];
          const c4 = [shape.x + offsetX, shape.y - offsetY];
          ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath();
        },
      });
      // 绘制顶面
      const CubeTop = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x, shape.y];
          const c2 = [shape.x + offsetX, shape.y - offsetY]; //右点
          const c3 = [shape.x, shape.y - offsetX];
          const c4 = [shape.x - offsetX, shape.y - offsetY];
          ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath();
        },
      });
      // 注册三个面图形
      echarts.graphic.registerShape('CubeLeft', CubeLeft);
      echarts.graphic.registerShape('CubeRight', CubeRight);
      echarts.graphic.registerShape('CubeTop', CubeTop);

      const VALUE = barDataObj.yData;

      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: function (params, ticket, callback) {
            const item = params[1];
            return item.name + ' : ' + item.value;
          },
        },
        grid: {
          left: '0%',
          right: '5%',
          top: '25%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: barDataObj.xData,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#3c5994',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            fontSize: 12,
            interval: 0,
            color:'#a5cdfa'
          },
        },
        yAxis: {
          show:false,
          type: 'value',
          axisLine: {
            show: true,
            lineStyle: {
              width: 2,
              color: '#2B7BD6',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#153D7D',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            fontSize: 12,
          },
          // boundaryGap: ['20%', '20%'],
        },
        series: [
          {
            type: 'custom',
            renderItem: (params, api) => {
              const location = api.coord([api.value(0), api.value(1)]);
              return {
                type: 'group',
                children: [
                  {
                    type: 'CubeLeft',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#0795f3',
                        },
                        {
                          offset: 1,
                          color: '#21dfff',
                        },
                      ]),
                    },
                  },
                  {
                    type: 'CubeRight',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#0583d7',
                        },
                        {
                          offset: 1,
                          color: '#1dbfdb',
                        },
                      ]),
                    },
                  },
                  {
                    type: 'CubeTop',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#06b8f9',
                        },
                        {
                          offset: 1,
                          color: '#09d8fb',
                        },
                      ]),
                    },
                  },
                ],
              };
            },
            data: VALUE,
            barCategoryGap: '30%',
          },
          {
            type: 'bar',
            label: {
              normal: {
                show: true,
                position: 'top',
                formatter: (e) => {
                  return e.value;
                  /*console.log(e)
                  switch (e.name) {
                      case '1001':
                          return e.value;
                      case '1002':
                          return VALUE[1];
                      case '1003':
                          return VALUE[2];
                  }*/
                },
                fontSize: 12,
                color: '#fff',
                offset: [0, -15],
              },
            },
            itemStyle: {
              color: 'transparent',
            },
            tooltip: {},
            data: VALUE,
            barCategoryGap: '30%',
          },
        ],
      };


      return option;
    },
  },

}
</script>

<style lang="less" scoped>
.ranking_box {
  margin-top: 15px;

  .ranking_con {
    width: 100%;
    height: 242px;
    // background: linear-gradient(to right, #F8FAFB, #1A59AE);
    box-sizing: border-box;
  }

}

.column_title {
  height: 43px;
  padding-left: 40px;
  background-size: 100% 100% !important;

  &.titlebg1 {
    background: url("~@/assets/images/bigScreenImg/titlebg2.png") left center no-repeat;
  }

  &.titlebg2 {
    background: url("~@/assets/images/bigScreenImg/titlebg1.png") left center no-repeat;
  }

  &.titlebg3 {
    background: url("~@/assets/images/bigScreenImg/titlebg3.png") left center no-repeat;
  }

  &.titlebg4 {
    background: url("~@/assets/images/bigScreenImg/titlebg4.png") left center no-repeat;
  }

  .tittext {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom,
        #bfebff 0%,
        #cef0ff 30%,
        #ffffff 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.4;
  }

  .tabitem {
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    background: linear-gradient(to bottom,
        #d1d5dd 0%,
        #acb6c6 30%,
        #939aa7 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.25;

    &.active {
      background: linear-gradient(to bottom,
          #bfebff 0%,
          #cef0ff 30%,
          #ffffff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }

}

.column_content {
  height: 242px;
  padding-top: 3px;
  background: linear-gradient(to bottom,
      rgba(26, 89, 174, 0.5) 0%,
      rgba(26, 89, 174, 0.1) 20%,
      rgba(26, 89, 174, 0) 50%);
  box-sizing: border-box;
}

.column_box {
  width: 100%;
  margin-top:40px;
}
</style>
