const account = {};

const access = [];
// permSign
account.buildMenuTree = function (data = [], sub) {
  const tree = [];
  const menus = cleanData(data, sub);
  // map映射
  const menusMap = {};
  for (let i = 0; i < menus.length; i++) {
    menusMap[menus[i].id] = menus[i];
  }
  // 遍历构建菜单树
  for (let i = 0; i < menus.length; i++) {
    if (menusMap[menus[i].parentId]) {
      if (Array.isArray(menusMap[menus[i].parentId].children)) {
        menusMap[menus[i].parentId].children.push(menus[i]);
      } else {
        menusMap[menus[i].parentId].children = [menus[i]];
      }
    } else {
      tree.push(menus[i]);
    }
  }
  return handleMenus(tree, sub);
};

// // 子应用单独调用时处理菜单路由
// fun
// 处理菜单和用户信息数据以满足一定的格式数据
function cleanData(data, sub) {
  const menus = [];
  for (let i = 0; i < data.length; i++) {
    data[i].permissionCode && access.push(data[i].permissionCode);
    if (data[i].permType !== 'button' && data[i].permType !== 'interface') {
      if (data[i].permPath.startsWith(`/${sub}`)) {
        data[i].permPath = data[i].permPath.replace(`/${sub}`, '');
      }
      const temp = {
        id: data[i].id,
        parentId: data[i].permParent,
        path: data[i].permPath,
        title: data[i].permName,
        icon: data[i].permIcon,
        name: data[i].permName,
        type: data[i].permType,
        auth: [data[i].permCode],
        permOrder: data[i].permOrder,
        // helpUrl: data[i].helpUrl
      };
      if (data[i].permType === 'module') temp.auth = ['hidden'];
      menus.push(temp);
    }
  }
  menus.sort((a, b) => {
    return a.permOrder - b.permOrder;
  });
  return menus;
}
function initHeaderPath(child) {
  if (child.children) {
    const temp = child.children.find((el) => el.type === 'sider');
    if (temp) {
      return initHeaderPath(temp);
    }
  }
  return child.path;
}
function handleMenus(menus) {
  const header = [];
  let sider = [];
  for (let i = 0; i < menus.length; i++) {
    if (menus[i].type === 'header') {
      header.push(menus[i]);
      if (Array.isArray(menus[i].children)) {
        let hideSider = true;
        for (let j = 0; j < menus[i].children.length; j++) {
          if (menus[i].children[j].type === 'sider') {
            hideSider = false;
          }
          menus[i].children[j].header = menus[i].name;
          sider.push(menus[i].children[j]);
        }
        menus[i].hideSider = hideSider;
      } else {
        console.error('存在根节点无children，id:', menus[i].id);
      }
    } else {
      console.error('存在根节点不是header类型，id:', menus[i].id);
    }
  }
  if (!header.length) {
    sider = menus;
  }
  header.length &&
    header.forEach((el) => {
      if (el.children && el.children.length) {
        el.path = initHeaderPath(el.children[0]);
      }
    });
  return { header, sider };
}

export default account;
