// 所有正则

// 校验用户名
export const regUserName = /^(\d|\w){4,20}$/;

// 校验邮箱
export const regEmail =
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

// 校验手机号
export const regPhone = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;

// 校验密码
// export const regPassword = /^(?=.*?[a-zA-Z])(?=.*?[0-9])(?=.*?[_\-@&=])[a-zA-z0-9_\-@&=]+$/
export const regPassword =
  /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z#@()\.\$%\^\&\*']+$)(?![a-z0-9]+$)(?![a-z#@()\.\$%\^\&\*']+$)(?![0-9#@()\.\$%\^\&\*']+$)[a-zA-Z0-9#@()\.\$%\^\&\*']{10,20}$/;

// 校验角色编码
export const regRoleCode = /^(?!.*__)(?!.*\/\/)(?!.*_\/)(?!.*\/_)(?=.*([a-zA-Z]))[_/a-zA-Z0-9]{4,50}$/;
