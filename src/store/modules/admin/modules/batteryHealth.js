/**
 * “电池健康”页面数据
 * */
export default {
  namespaced: true,
  state: {
    // 电池画像
    batteryPortrait: {
      refreshIndex: 1,
      isShowSearchForm: false,
      vin: '',
    },
  },
  mutations: {
    setBatteryPortraitIsShowSearchForm(state, isShow) {
      state.batteryPortrait.isShowSearchForm = isShow;
    },
    triggerBatteryPortraitRefresh(state) {
      if (state.batteryPortrait.refreshIndex > 9) {
        state.batteryPortrait.refreshIndex = 1;
      } else {
        state.batteryPortrait.refreshIndex++;
      }
    },
    setBatteryPortraitVin(state, vin) {
      state.batteryPortrait.vin = vin;
    },
  },
};
