import API from '@/api/config/index';

// 校验（ruleType: 'length'）
export const LENGTH_MAX_INPUT = 20; // input 最大可以输入的字符数
export const LENGTH_MAX_INPUT_LONG = 200; // "备注"等字段的最大可以输入的字符数

// 校验（ruleType: 'range'）
export const RANGE_MAX_FLOAT = 999; // 普通数值的最大值
export const RANGE_MIN_FLOAT = 1; // 普通数值的最小值
export const MSG_RANGE_INTEGER = '请输入1-999范围内的整数';
export const MSG_RANGE_FLOAT = '请输入1-999范围内的数值（可保留两位小数）';

// 不分页的时候
export const SIZE_NO_PAGE = 9999;
export const PAGE_NO_PAGE = 0;

// 上传
export const URL_FILE_GROUP = `${API.fileUrl}/`; // 获取图片的 url
export const URL_UPLOAD_FILE = `${API.baseUrl}${API.authAPIUrl}${API.prefixAPI}/files/upload`; // 上传文件的 url
export const IMPORT_LIMIT = {
  uploadFileMax: 1,
  suffixMsg: '支持 XLS、XLSX 格式',
  uploadAcceptFileType: 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  uploadFormatError: '只支持上传 XLS、XLSX 格式的文件!',
  uploadFileMaxSize: 0, // 不限制文件大小
};

// 后端返回的特殊 code
export const CODE_SOME_DEL_FAILED = 139001; // 部分删除失败

// 卡状态颜色
export const COLOR_CARD_STATUS = {
  130901: 'geekblue',
  130902: 'orange',
  130903: 'purple',
  130904: 'green',
  130905: 'red',
  130906: 'yellow',
  130907: 'grey',
};

// 车辆/电池故障类型对应的图标
export const CAR_ALARM_ICON = {
  temperatureDifference: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_temp.png', // 温度差异
  temperatureHigh: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_battery.png', // 电池高温
  socHigh: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_soc2.png', // soc过高
  socLow: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_soc3.png', // SOC过低
  socHop: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_soc1.png', // SOC跳变
  voltageHigh: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_voltage1.png', // 单体电压过高
  voltageLow: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_voltage2.png', // 单体电压过低
  batteryConsistencyDifference: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_consistency.png', // 电池单体一致性差
  driveTemperature: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_drive_temperature.png', // 驱动电机温度报警
  driveControllerTemperature: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_drive_controller_temperature.png', // 驱动电机控制器温度报警
  storageDeviceOvervoltage: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_overvoltage.png', // 车载储能装置类型过压
  storageDeviceUndervoltage: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_undervoltage.png', // 车载储能装置类型欠压
  storageDeviceOvercharge: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_overcharge.png', // 车载储能装置类型过充
  brakeSystem: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_brake.png', // 制动系统报警
  'dc-dcStatus': '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_dc_status.png', // DC-DC状态报警
  'dc-dcTemperature': '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_dc_temperature.png', // DC-DC温度报警
  pressureInterlocking: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_locking.png', // 高压互锁状态
  storageDeviceMismatching: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_mismatching.png', // 可充电储能系统不匹配
  insulation: '@/assets/images/batteryHealth/batteryPortrait/alarmType/icon_insulation.png', // 绝缘报警
};
