<template>
  <span class="i-layout-header-trigger i-layout-header-trigger-min">
    <Dropdown :trigger="isMobile ? 'click' : 'hover'" class="i-layout-header-user" :class="{ 'i-layout-header-user-mobile': isMobile }" @on-click="handleClick">
      <!-- <Avatar size="small" :src="info.avatar" v-if="info.avatar" /> -->
      <Avatar v-if="info.avatar1" size="small" :src="info.avatar" />
      <Avatar v-else-if="info.gender === 'm'" size="small" :src="requireImg('@/assets/images/avator-m.png')" />
      <Avatar v-else size="small" :src="requireImg('@/assets/images/avator-f.png')" />
      <span v-if="!isMobile" class="i-layout-header-user-name">{{ info.realname }}</span>
      <template v-slot:list>
        <DropdownMenu>
          <i-link to="/personal">
            <DropdownItem>
              <Icon type="ios-contact-outline" />
              <span>{{ $t('basicLayout.user.center') }}</span>
            </DropdownItem>
          </i-link>
          <!-- <i-link to="/setting/account">
                    <DropdownItem>
                        <Icon type="ios-settings-outline" />
                        <span>{{ $t('basicLayout.user.setting') }}</span>
                    </DropdownItem>
                </i-link> -->
          <DropdownItem divided name="logout">
            <Icon type="ios-log-out" />
            <span>{{ $t('basicLayout.user.logOut') }}</span>
          </DropdownItem>
        </DropdownMenu>
      </template>
    </Dropdown>
  </span>
</template>

<script>
import { mapActions, mapState } from 'vuex';

export default {
  name: 'IHeaderUser',
  computed: {
    ...mapState('admin/user', ['info']),
    ...mapState('admin/layout', ['isMobile', 'logoutConfirm']),
  },
  methods: {
    ...mapActions('admin/account', ['logout']),
    handleClick(name) {
      if (name === 'logout') {
        this.logout({
          confirm: this.logoutConfirm,
          vm: this,
        });
      }
    },
  },
};
</script>
