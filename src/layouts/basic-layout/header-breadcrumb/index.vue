<template>
  <Breadcrumb v-if="!isLimit" ref="breadcrumb" class="i-layout-header-breadcrumb">
    <BreadcrumbItem v-if="headerMenu" :to="topItem ? topItem.path : ''">
      <i-menu-head-title :item="topItem" :hide-icon="!showBreadcrumbIcon" />
    </BreadcrumbItem>
    <BreadcrumbItem v-for="item in items" :key="item.path" :to="matchFullPath(item.path)">
      <i-menu-head-title v-if="item" :item="item" :hide-icon="!showBreadcrumbIcon" />
    </BreadcrumbItem>
    <BreadcrumbItem v-if="activePath">
      <i-menu-head-title :item="siderMenuObject[activePath]" :hide-icon="!showBreadcrumbIcon" />
    </BreadcrumbItem>
  </Breadcrumb>
</template>

<script>
import { mapState } from 'vuex';
import { off, on } from 'view-ui-plus/src/utils/dom';
import { findComponentUpward, getStyle } from 'view-ui-plus/src/utils/assist';
import { throttle } from 'lodash';
import iMenuHeadTitle from '../menu-head/title';
import { flattenSiderMenu } from '@/libs/system';

export default {
  name: 'IHeaderBreadcrumb',
  components: { iMenuHeadTitle },
  data() {
    return {
      handleResize: () => {},
      isLimit: false,
      maxWidth: 560,
      breadcrumbWidth: 0,
    };
  },
  computed: {
    ...mapState('admin/page', ['opened']),
    ...mapState('admin/layout', ['showBreadcrumbIcon', 'menuCollapse', 'headerMenu']),
    ...mapState('admin/menu', ['openNames', 'activePath', 'header', 'headerName', 'menuSider']),
    siderMenuObject() {
      const obj = {};
      this.allSiderMenu.forEach((item) => {
        if ('path' in item) {
          obj[item.path] = item;
        }
      });
      console.log(obj);
      return obj;
    },
    items() {
      const items = [...this.openNames];
      const newItems = [];
      items.forEach((i) => {
        newItems.push(this.siderMenuObject[i]);
      });
      return newItems;
    },
    // 第一级，默认是 menu/header.js 中的第一项
    topItem() {
      return this.header.find((item) => item.name === this.headerName);
    },
    // 得到所有侧边菜单，并转为平级，查询图标及显示对应内容
    allSiderMenu() {
      return flattenSiderMenu(this.menuSider, []);
    },
  },
  watch: {
    topItem: {
      handler() {
        this.handleGetWidth();
        this.handleCheckWidth();
      },
      deep: true,
    },
    items: {
      handler() {
        this.handleGetWidth();
        this.handleCheckWidth();
      },
      deep: true,
    },
    activePath: {
      handler() {
        this.handleGetWidth();
        this.handleCheckWidth();
      },
      deep: true,
    },
  },
  mounted() {
    this.handleResize = throttle(this.handleCheckWidth, 100, {
      leading: false,
    });
    on(window, 'resize', this.handleResize);
    this.handleGetWidth();
    this.handleCheckWidth();
  },
  beforeUnmount() {
    off(window, 'resize', this.handleResize);
  },
  methods: {
    handleCheckWidth() {
      const $header = findComponentUpward(this, 'Header');
      if ($header) {
        const headerWidth = parseInt(getStyle($header.$el, 'width'));
        this.$nextTick(() => {
          this.isLimit = headerWidth - this.maxWidth <= this.breadcrumbWidth;
        });
      }
    },
    handleGetWidth() {
      this.isLimit = false;
      this.$nextTick(() => {
        const $breadcrumb = this.$refs.breadcrumb;
        if ($breadcrumb) {
          this.breadcrumbWidth = parseInt(getStyle($breadcrumb.$el, 'width'));
        }
      });
    },
    matchFullPath(path) {
      for (let i = 0; i < this.opened.length; i++) {
        if (path === this.opened[i].path) {
          return this.opened[i].fullPath;
        }
      }
      return '';
    },
  },
};
</script>
