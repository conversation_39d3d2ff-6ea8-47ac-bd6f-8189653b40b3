<template>
  <span class="i-layout-header-trigger i-layout-header-trigger-min i-layout-header-trigger-in">
    <Notification
      :wide="isMobile"
      :badge-props="badgeProps"
      class="i-layout-header-notice"
      :class="{ 'i-layout-header-notice-mobile': isMobile }"
      auto-count
      @on-load-more="handleLoadMore"
      @on-clear="handleClear"
    >
      <template v-slot:icon>
        <Icon custom="i-icon i-icon-notification" />
      </template>
      <NotificationTab
        title="通知"
        name="message"
        :count="unreadMessage"
        :loaded-all="messageList.length >= 15"
        :loading="messageLoading"
        :scroll-to-load="false"
      >
        <NotificationItem
          v-for="(item, index) in messageList"
          :key="index"
          :title="item.title"
          :icon="item.icon"
          :icon-color="item.iconColor"
          :time="item.time"
          :read="item.read"
        />
      </NotificationTab>
      <NotificationTab title="关注" name="follow" :count="unreadFollow" :loaded-all="followList.length >= 15" :loading="followLoading" :scroll-to-load="false">
        <NotificationItem v-for="(item, index) in followList" :key="index" :avatar="item.avatar" :title="item.title" :time="item.time" :read="item.read" />
      </NotificationTab>
      <NotificationTab title="待办" name="todo" :count="unreadTodo" :loaded-all="todoList.length >= 15" :loading="todoLoading" :scroll-to-load="false">
        <NotificationItem
          v-for="(item, index) in todoList"
          :key="index"
          :title="item.title"
          :content="item.content"
          :tag="item.tag"
          :tag-props="item.tagProps"
          :read="item.read"
        />
      </NotificationTab>
    </Notification>
  </span>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'IHeaderNotice',
  data() {
    return {
      badgeProps: {
        offset: [20, 0],
      },
      messageBaseList: [
        {
          icon: 'md-home',
          iconColor: '#87d068',
          title: '欢迎使用比翼微前端模板',
          read: 0,
          time: +new Date(),
        },
      ],
      followBaseList: [],
      todoBaseList: [],
      messageList: [],
      followList: [],
      todoList: [],
      messageLoading: false,
      followLoading: false,
      todoLoading: false,
    };
  },
  computed: {
    ...mapState('admin/layout', ['isMobile']),
    unreadMessage() {
      let unread = 0;
      this.messageList.forEach((item) => {
        if (!item.read) {
          unread++;
        }
      });
      return unread;
    },
    unreadFollow() {
      let unread = 0;
      this.followList.forEach((item) => {
        if (!item.read) {
          unread++;
        }
      });
      return unread;
    },
    unreadTodo() {
      let unread = 0;
      this.todoList.forEach((item) => {
        if (!item.read) {
          unread++;
        }
      });
      return unread;
    },
  },
  mounted() {
    this.messageList = [...this.messageBaseList];
    this.followList = [...this.followBaseList];
    this.todoList = [...this.todoBaseList];
  },
  methods: {
    handleLoadMore(tab) {
      this.loadMore(tab.name);
    },
    loadMore(type) {
      if (this[`${type}Loading`]) {
        return;
      }
      this[`${type}Loading`] = true;
      setTimeout(() => {
        this[`${type}List`] = this[`${type}List`].concat([...this[`${type}BaseList`]]);
        this[`${type}Loading`] = false;
      }, 1000);
    },
    handleClear(tab) {
      this.clearUnread(tab.name);
    },
    clearUnread(type) {
      this[`${type}List`] = this[`${type}List`].map((item) => {
        item.read = 1;
        return item;
      });
    },
  },
};
</script>
