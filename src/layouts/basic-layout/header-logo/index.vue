<template>
  <i-link class="i-layout-header-logo" :class="{ 'i-layout-header-logo-stick': !isMobile }" to="/">
    <img v-if="isMobile" src="@/assets/images/logo-small.png" />
    <img v-else src="@/assets/images/logo.png" />
  </i-link>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'IHeaderLogo',
  computed: {
    ...mapState('admin/layout', ['isMobile', 'headerTheme']),
  },
};
</script>
