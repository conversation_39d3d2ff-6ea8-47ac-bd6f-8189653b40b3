<template>
  <span v-if="isDesktop" class="i-layout-header-trigger i-layout-header-trigger-min i-layout-header-trigger-in i-layout-header-trigger-nohover">
    <input class="i-layout-header-search" type="text" :placeholder="$t('basicLayout.search.placeholder')" />
  </span>
  <span v-else class="i-layout-header-trigger i-layout-header-trigger-min">
    <Dropdown ref="dropdown" trigger="click" class="i-layout-header-search-drop">
      <Icon type="ios-search" />
      <template v-slot:list>
        <DropdownMenu>
          <div class="i-layout-header-search-drop-main">
            <Input size="large" prefix="ios-search" type="text" :placeholder="$t('basicLayout.search.placeholder')" />
            <span class="i-layout-header-search-drop-main-cancel" @click="handleCloseSearch">{{ $t('basicLayout.search.cancel') }}</span>
          </div>
        </DropdownMenu>
      </template>
    </Dropdown>
  </span>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'IHeaderSearch',
  computed: {
    ...mapState('admin/layout', ['isDesktop', 'headerMenu']),
  },
  methods: {
    handleCloseSearch() {
      this.$refs.dropdown.handleClick();
    },
  },
};
</script>
