<template>
  <span class="i-layout-header-trigger i-layout-header-trigger-min" @click="toggleFullscreen">
    <Icon v-show="!isFullscreen" custom="i-icon i-icon-full-screen" />
    <Icon v-show="isFullscreen" custom="i-icon i-icon-exit-full-screen" />
  </span>
</template>

<script>
import { mapActions, mapState } from 'vuex';

export default {
  name: 'IHeaderFullscreen',
  computed: {
    ...mapState('admin/layout', ['isFullscreen']),
  },
  methods: {
    ...mapActions('admin/layout', ['toggleFullscreen']),
  },
};
</script>
