import biyiAuthUtil from '../biyi-auth/init';
import carBrand from './modules/asset/car-brand'; // 品牌管理
import carList from './modules/asset/car-list'; // 车辆管理
import carModel from './modules/asset/car-model'; // 车型管理
import Audit from './modules/audit/audit'; // 安全审计
import bigmodel from './modules/big-model/bigmodel';
import bigscreen from './modules/big-screen/bigscreen'; // 安全事件流程管理
import Demo from './modules/demo'; // 示例
import Home from './modules/home';
import EmergencyWorkOrder from './modules/safety/emergency-work-order'; // 应急处置工单管理
import EmergencyWorkflow from './modules/safety/emergency-workflow';// 安全事件流程管理
import WarnEventManage from './modules/safety/warn-event-manage'; // 告警事件统一管理
import Firewall from './modules/strategy/firewall'; // 示例
import IntrusionSnort from './modules/strategy/intrusion-snort'; // 示例
// import Strategy from './modules/strategy/strategy'; // 示例
import Base from './modules/system/base';
import ErrorPage from './modules/system/error';
import Log from './modules/system/log';
import attattackSource from './modules/safetyDiagnosis/attattack-source'; // 攻击源分析
import carRelation from './modules/safetyDiagnosis/car-relation'; // 多车关联分析
import complexAttack from './modules/safetyDiagnosis/complex-attack'; // 复杂攻击分析

/**
 * 路由基本配置
 */
// [MEMO VU3升级] 暂时只搬运 登录 和 个人中心 页面
const { LoginRoute, personalRoute } = biyiAuthUtil.getInit();

const base = [...Base];

/**
 * 在主框架内显示
 */

const frameIn = [
  // 注意：正常页面的路由请不要以 /INGPAGE 结尾
  personalRoute,
  Home,
  Log,
  ...Demo,
  // ...Strategy,
  ...carBrand,
  ...carList,
  ...carModel,
  ...Audit,
  ...WarnEventManage,
  ...EmergencyWorkOrder,
  ...EmergencyWorkflow,
  ...bigmodel,
  ...attattackSource,
  ...carRelation,
  ...complexAttack,
  ...Firewall,
  ...IntrusionSnort,
  ...bigscreen,
  // ...Asset,
  // ------------------ 以 /INGPAGE 结尾的路由，跳转到“开发中”页面 ------------------
  {
    path: '/:c(\\w*)*/INGPAGE',
    name: 'ingPage',
    meta: {
      skipAuth: true,
    },
    component: () => import('@/views/common/ing.vue'),
  },
];

/**
 * 在主框架之外显示
 */

const frameOut = [
  // 登录
  {
    ...LoginRoute,
    path: '/login',
    name: 'login',
    meta: {
      title: '$t:page.login.title', // TODO
      frameOut: true,
    },
  },
  // 放在最下面
  ...ErrorPage,
];
// 确保每个在frameIn下的路由有auth属性
frameIn.forEach((v) => {
  if (v.meta) {
    if (!v.meta.skipAuth) {
      !Object.prototype.hasOwnProperty.call(v.meta, 'auth') && (v.meta.auth = true);
    }
  } else {
    v.meta = {
      auth: true,
    };
  }
});

// 导出需要显示菜单的
export const frameInRoutes = frameIn;

// 重新组织后导出
export default [...base, ...frameIn, ...frameOut];
