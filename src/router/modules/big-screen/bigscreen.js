/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
    // ------------------ 关键词 ------------------
    {
        path: '/safestate/safestate-asset',
        name: 'safestate-asset',
        meta: {
            title: '资产风险态势',
            frameOut: true
        },
        component: () => import('@/views/bigsreen/carpage.vue')
    },
    {
        path: '/safestate/safestate-car',
        name: 'safestate-car',
        meta: {
            title: '车辆综合安全态势',
            frameOut: true
        },
        component: () => import('@/views/bigsreen/carpagetwo.vue')
    }
];
