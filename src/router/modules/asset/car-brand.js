/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
  // ------------------ 有增删改查功能的列表页面 ------------------
  {
    path: '/asset-mange/car-brand-list',
    name: 'car-brand-list',
    meta: {
      auth: 'ivsmp.asset-mange.car-brand-list',
      title: '品牌管理',
      requiredId: false,
    },
    component: () => import('@/views/asset/car-brand'),
  },
  {
    path: '/asset-mange/car-brand-add',
    name: 'car-brand-add',
    meta: {
      auth: 'ivsmp.asset-mange.car-brand-add',
      title: '添加品牌',
      confirmLeave: true,
    },
    component: () => import('@/views/asset/car-brand/addOrEdit'),
  },
  {
    path: '/asset-mange/car-brand-edit',
    name: 'car-brand-edit',
    meta: {
      auth: 'ivsmp.asset-mange.car-brand-edit',
      title: '编辑品牌',
      confirmLeave: false,
    },
    component: () => import('@/views/asset/car-brand/addOrEdit'),
  },
];
