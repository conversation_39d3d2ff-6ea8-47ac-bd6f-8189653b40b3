/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
  {
    path: '/asset-mange/car-list',
    name: 'car-list',
    meta: {
      auth: 'ivsmp.asset-mange.car-list',
      title: '车辆管理',
      requiredId: false,
    },
    component: () => import('@/views/asset/car-list'),
  },
  {
    path: '/asset-mange/car-add',
    name: 'car-add',
    meta: {
      auth: 'ivsmp.asset-mange.car-add',
      title: '新增车辆',
      requiredId: false,
      confirmLeave: true
    },
    component: () => import('@/views/asset/car-list/addOrEdit'),
  },
  {
    path: '/asset-mange/car-edit',
    name: 'car-edit',
    meta: {
      auth: 'ivsmp.asset-mange.car-edit',
      title: '编辑车辆',
      requiredId: true,
      confirmLeave: true,
    },
    component: () => import('@/views/asset/car-list/addOrEdit'),
  },
  {
    path: '/asset-mange/car-detail',
    name: 'car-detail',
    meta: {
      auth: 'ivsmp.asset-mange.car-detail',
      title: '查看车辆',
      requiredId: true
    },
    component: () => import('@/views/asset/car-list/addOrEdit'),
  }
];
