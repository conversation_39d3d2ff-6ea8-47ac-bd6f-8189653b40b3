/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
  // ------------------ 有增删改查功能的列表页面 ------------------
  {
    path: '/asset-mange/car-model-list',
    name: 'car-model-list',
    meta: {
      auth: 'ivsmp.asset-mange.car-model-list',
      title: '车型管理',
      requiredId: false,
    },
    component: () => import('@/views/asset/car-model'),
  },
  {
    path: '/asset-mange/car-model-add',
    name: 'car-model-add',
    meta: {
      auth: 'ivsmp.asset-mange.car-model-add',
      title: '新增车型',
      requiredId: false,
      confirmLeave: true
    },
    component: () => import('@/views/asset/car-model/addOrEdit'),
  },
  {
    path: '/asset-mange/car-model-edit',
    name: 'car-model-edit',
    meta: {
      auth: 'ivsmp.asset-mange.car-model-edit',
      title: '编辑车型',
      requiredId: true,
      confirmLeave: true
    },
    component: () => import('@/views/asset/car-model/addOrEdit'),
  }
];
