/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
    // ------------------ 关键词 ------------------
    {
        path: '/bigmodel/keyWord',
        name: 'keyWord-list',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.gjcgl',
        },
        component: () => import('@/views/bigModel/keyWord'),
    },
    {
        path: '/bigmodel/keyWord/detail',
        name: 'keyWord-list-detail',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.gjcgl.detail',
            title: '查看',
            requiredId: true,
        },
        component: () => import('@/views/bigModel/keyWord/detail.vue'),
    },
    {
        path: '/bigmodel/keyWord/add',
        name: 'keyWord-list-add',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.gjcgl.add',
            title: '新增',
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/keyWord/detail.vue'),
    },
    {
        path: '/bigmodel/keyWord/edit',
        name: 'keyWord-list-edit',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.gjcgl.edit',
            title: '修改',
            requiredId: true,
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/keyWord/detail.vue'),
    },
    {
        path: '/bigmodel/keyWord/delete',
        name: 'keyWord-list-delete',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.gjcgl.delete',
            title: '删除',
            requiredId: true,
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/keyWord/detail.vue'),
    },
    // ------------------ 敏感词 ------------------
    {
        path: '/bigmodel/mgword',
        name: 'mgword-list',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.mgcgl',
        },
        component: () => import('@/views/bigModel/mgword'),
    },
    {
        path: '/bigmodel/mgword/detail',
        name: 'mgword-list-detail',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.mgcgl.detail',
            title: '查看',
            requiredId: true,
        },
        component: () => import('@/views/bigModel/mgword/detail.vue'),
    },
    {
        path: '/bigmodel/mgword/add',
        name: 'mgword-list-add',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.mgcgl.add',
            title: '新增',
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/mgword/detail.vue'),
    },
    {
        path: '/bigmodel/mgword/edit',
        name: 'mgword-list-edit',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.mgcgl.edit',
            title: '修改',
            requiredId: true,
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/mgword/detail.vue'),
    },
    {
        path: '/bigmodel/mgword/delete',
        name: 'mgword-list-delete',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.mgcgl.delete',
            title: '删除',
            requiredId: true,
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/mgword/detail.vue'),
    },
    // ------------------ 告警模块 ------------------
    {
        path: '/bigmodel/warn',
        name: 'bigmodel-warn-list',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.warn',
        },
        component: () => import('@/views/bigModel/warn'),
    },
    {
        path: '/bigmodel/warn/detail',
        name: 'bigmodel-warn-list-detail',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.warn.detail',
            title: '查看',
            requiredId: true,
        },
        component: () => import('@/views/bigModel/warn/detail.vue'),
    },
    {
        path: '/bigmodel/warn/add',
        name: 'bigmodel-warn-list-add',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.warn.add',
            title: '新增',
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/warn/detail.vue'),
    },
    {
        path: '/bigmodel/warn/edit',
        name: 'bigmodel-warn-list-edit',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.warn.edit',
            title: '修改',
            requiredId: true,
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/warn/detail.vue'),
    },
    {
        path: '/bigmodel/warn/delete',
        name: 'bigmodel-warn-list-delete',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.warn.delete',
            title: '删除',
            requiredId: true,
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/warn/detail.vue'),
    },
    // ------------------ 审核模块 ------------------
    {
        path: '/bigmodel/audit',
        name: 'bigmodel-audit-list',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.audit',
        },
        component: () => import('@/views/bigModel/audit'),
    },
    {
        path: '/bigmodel/audit/detail',
        name: 'bigmodel-audit-list-detail',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.audit.detail',
            title: '查看',
            requiredId: true,
        },
        component: () => import('@/views/bigModel/audit/detail.vue'),
    },
    {
        path: '/bigmodel/audit/add',
        name: 'bigmodel-audit-list-add',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.audit.add',
            title: '新增',
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/audit/detail.vue'),
    },
    {
        path: '/bigmodel/audit/edit',
        name: 'bigmodel-audit-list-edit',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.audit.edit',
            title: '修改',
            requiredId: true,
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/audit/detail.vue'),
    },
    {
        path: '/bigmodel/audit/delete',
        name: 'bigmodel-audit-list-delete',
        meta: {
            auth: 'ivsmp.big-model.safety-fence.audit.delete',
            title: '删除',
            requiredId: true,
            confirmLeave: true,
        },
        component: () => import('@/views/bigModel/audit/detail.vue'),
    },
];
