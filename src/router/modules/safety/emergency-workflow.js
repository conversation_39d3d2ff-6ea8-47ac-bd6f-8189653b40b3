/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */

// 安全事件流程管理
export default [
    // ------------------ 有增删改查功能的列表页面 ------------------
    {
        path: '/emergency-workflow/list',
        name: 'emergency-workflow-list',
        meta: {
            auth: 'ivsmp.waemergency-workflow.list',
            title: '安全事件流程管理',
            // requiredId: true,
        },
        component: () => import('@/views/safety/emergency-workflow/index.vue'),
    },
    {
        path: '/emergency-workflow/edit',
        name: 'emergency-workflow-edit',
        meta: {
            auth: 'ivsmp.waemergency-workflow.edit',
            title: '安全事件流程管理',
        },
        component: () => import('@/views/safety/emergency-workflow/edit.vue'),
    },
];
