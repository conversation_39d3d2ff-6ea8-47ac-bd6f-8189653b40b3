/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */

// 告警事件统一管理
export default [
    // ------------------ 有增删改查功能的列表页面 ------------------
    {
        path: '/warn-event-manage/list',
        name: 'warn-event-manage-list',
        meta: {
            auth: 'ivsmp.warn-event-manage.list',
            title: '告警事件统一管理',
            // requiredId: true,
        },
        component: () => import('@/views/safety/warn-event-manage/index.vue'),
    },
    {
        path: '/warn-event-manage/detail',
        name: 'warn-event-manage-detail',
        meta: {
            auth: 'ivsmp.warn-event-manage.detail',
            title: '告警事件统一管理',
            // requiredId: true,
        },
        component: () => import('@/views/safety/warn-event-manage/detail.vue'),
    },
    {
        path: '/warn-event-manage/add',
        name: 'warn-event-manage-add',
        meta: {
            auth: 'ivsmp.warn-event-manage.add',
            title: '告警事件统一管理',
            // requiredId: true,
        },
        component: () => import('@/views/safety/warn-event-manage/add.vue'),
    },
    {
        path: '/warn-event-manage/relations',
        name: 'warn-event-manage-relations',
        meta: {
            auth: 'ivsmp.warn-event-manage.relations',
            title: '告警事件统一管理-关联事件详情',
            // requiredId: true,
        },
        component: () => import('@/views/safety/warn-event-manage/relations.vue'),
    },
    // {
    //     path: '/warn-event-manage/deal',
    //     name: 'warn-event-manage-deal',
    //     meta: {
    //         auth: 'ivsmp.warn-event-manage.deal',
    //         title: '告警事件统一管理',
    //         // requiredId: true,
    //     },
    //     component: () => import('@/views/safety/warn-event-manage/deal.vue'),
    // },
];
