/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */

// 应急处置工单管理
export default [
    // ------------------ 有增删改查功能的列表页面 ------------------
    {
        path: '/emergency-work-order/list',
        name: 'emergency-work-order-list',
        meta: {
            auth: 'ivsmp.waemergency-work-order.list',
            title: '应急处置工单管理',
            // requiredId: true,
        },
        component: () => import('@/views/safety/emergency-work-order/index.vue'),
    },
    {
        path: '/emergency-work-order/detail',
        name: 'emergency-work-order-detail',
        meta: {
            auth: 'ivsmp.waemergency-work-order.detail',
            title: '应急处置工单管理-详情',
        },
        component: () => import('@/views/safety/emergency-work-order/detail.vue'),
    },
    {
        path: '/emergency-work-order/flow',
        name: 'emergency-work-order-flow',
        meta: {
            auth: 'ivsmp.waemergency-work-order.processinfo',
            title: '应急处置工单管理-流程信息',
        },
        component: () => import('@/views/safety/emergency-work-order/flow.vue'),
    },
];
