/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
  // ------------------ 有增删改查功能的列表页面 ------------------
  {
    path: '/strategy-mange/firewall-list',
    name: 'firewall-list',
    meta: {
      auth: 'ivsmp.strategy-mange.firewall-list',
      title: '防火墙规则',
      // requiredId: true,
    },
    component: () => import('@/views/strategy/firewall'),
  },
  {
    path: '/strategy-mange/firewall-list/add',
    name: 'firewall-list-add',
    meta: {
      auth: 'ivsmp.strategy-mange.firewall-add',
      title: '添加防火墙规则',
      // confirmLeave: true,
    },
    component: () => import('@/views/strategy/firewall/detail.vue'),
  },
  {
    path: '/strategy-mange/firewall-list/edit',
    name: 'firewall-list-edit',
    meta: {
      auth: 'ivsmp.strategy-mange.firewall-edit',
      title: '修改防火墙规则',
      // confirmLeave: true,
    },
    component: () => import('@/views/strategy/firewall/detail.vue'),
  },
  {
    path: '/strategy-mange/firewall-list/detail',
    name: 'firewall-list-detail',
    meta: {
      auth: 'ivsmp.strategy-mange.firewall-detail',
      title: '查看防火墙规则',
      // confirmLeave: true,
    },
    component: () => import('@/views/strategy/firewall/detail.vue'),
  },
  {
    path: '/strategy-mange/firewall-list/rule-add',
    name: 'firewall-list-rule-add',
    meta: {
      auth: 'ivsmp.strategy-mange.firewall-rule-add',
      title: '添加防火墙策略',
      // confirmLeave: true,
    },
    component: () => import('@/views/strategy/firewall/rule-detail.vue'),
  },
  {
    path: '/strategy-mange/firewall-list/rule-edit/:record',
    name: 'firewall-list-rule-edit',
    meta: {
      auth: 'ivsmp.strategy-mange.firewall-rule-edit',
      title: '修改防火墙策略',
      // confirmLeave: true,
    },
    component: () => import('@/views/strategy/firewall/rule-detail.vue'),
  },
  {
    path: '/strategy-mange/firewall-list/rule-detail/:record',
    name: 'firewall-list-rule-detail',
    meta: {
      auth: 'ivsmp.strategy-mange.firewall-rule-detail',
      title: '查看防火墙策略',
      // confirmLeave: true,
    },
    component: () => import('@/views/strategy/firewall/rule-detail.vue'),
  },
];
