/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
  // ------------------ 有增删改查功能的列表页面 ------------------
  {
    path: '/strategy-mange/intrusion-snort-list',
    name: 'intrusion-snort-list',
    meta: {
      auth: 'ivsmp.strategy-mange.intrusion-snort-list',
      title: '入侵检测规则',
      // requiredId: true,
    },
    component: () => import('@/views/strategy/intrusion-snort'),
  },
  {
    path: '/strategy-mange/intrusion-snort-list/add',
    name: 'intrusion-snort-add',
    meta: {
      auth: 'ivsmp.strategy-mange.intrusion-snort-add',
      title: '添加入侵检测规则',
      // confirmLeave: true,
    },
    component: () => import('@/views/strategy/intrusion-snort/detail.vue'),
  },
  {
    path: '/strategy-mange/intrusion-snort-list/edit/:record',
    name: 'intrusion-snort-edit',
    meta: {
      auth: 'ivsmp.strategy-mange.intrusion-snort-edit',
      title: '修改入侵检测规则',
      // confirmLeave: true,
      requiredId: true,
    },
    component: () => import('@/views/strategy/intrusion-snort/detail.vue'),
  },
  {
    path: '/strategy-mange/intrusion-snort-list/detail/:record',
    name: 'intrusion-snort-detail',
    meta: {
      auth: 'ivsmp.strategy-mange.intrusion-snort-detail',
      title: '查看入侵检测规则',
      // confirmLeave: true,
      requiredId: true,
    },
    component: () => import('@/views/strategy/intrusion-snort/detail.vue'),
  },
];
