/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
    // ------------------ 有增删改查功能的列表页面 ------------------
    {
        path: '/audit/audit-list',
        name: 'audit-list',
        meta: {
            auth: 'ivsmp.audit.audit-list',
            title: '安全审计',
            requiredId: false,
        },
        component: () => import('@/views/audit/index.vue'),
    },
    {
        path: '/audit/audit-detail',
        name: 'audit-detail',
        meta: {
            auth: 'ivsmp.audit.audit-detail',
            title: '安全审计-详情',
            // confirmLeave: true,
        },
        component: () => import('@/views/audit/detail.vue'),
    },
];