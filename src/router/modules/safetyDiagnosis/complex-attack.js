/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
    // ------------------ 有增删改查功能的列表页面 ------------------
    {
      path: '/complex-attack/list',
      name: 'complex-attack-list',
      meta: {
        auth: 'ivsmp.complex-attack.list',
        title: '复杂攻击分析',
        requiredId: false,
      },
      component: () => import('@/views/safetyDiagnosis/complex-attack')
    },
    {
      path: '/complex-attack/add',
      name: 'complex-attack-add',
      meta: {
        auth: 'ivsmp.complex-attack.add',
        title: '添加',
        confirmLeave: true,
      },
      component: () => import('@/views/safetyDiagnosis/complex-attack/operate')
    },
    {
      path: '/complex-attack/edit',
      name: 'complex-attack-edit',
      meta: {
        auth: 'ivsmp.complex-attack.edit',
        title: '编辑',
        confirmLeave: false,
      },
      component: () => import('@/views/safetyDiagnosis/complex-attack/operate')
    },
    {
      path: '/complex-attack/detail',
      name: 'complex-attack-detail',
      meta: {
        auth: 'ivsmp.complex-attack.detail',
        title: '查看',
        confirmLeave: false,
      },
      component: () => import('@/views/safetyDiagnosis/complex-attack/operate')
    }
];
