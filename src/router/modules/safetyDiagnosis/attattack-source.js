/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
    // ------------------ 有增删改查功能的列表页面 ------------------
    {
      path: '/attattack-source/list',
      name: 'attattack-source-list',
      meta: {
        auth: 'ivsmp.attattack-source.list',
        title: '攻击源分析',
        requiredId: false,
      },
      component: () => import('@/views/safetyDiagnosis/attattack-source')
    },
    {
      path: '/attattack-source/add',
      name: 'attattack-source-add',
      meta: {
        auth: 'ivsmp.attattack-source.add',
        title: '添加',
        confirmLeave: true,
      },
      component: () => import('@/views/safetyDiagnosis/attattack-source/operate')
    },
    {
      path: '/attattack-source/edit',
      name: 'attattack-source-edit',
      meta: {
        auth: 'ivsmp.attattack-source.edit',
        title: '编辑',
        confirmLeave: false
      },
      component: () => import('@/views/safetyDiagnosis/attattack-source/operate')
    },
    {
      path: '/attattack-source/detail',
      name: 'attattack-source-detail',
      meta: {
        auth: 'ivsmp.attattack-source.detail',
        title: '查看',
        confirmLeave: false
      },
      component: () => import('@/views/safetyDiagnosis/attattack-source/operate')
    }
];
