/**
 * meta
 *  auth   路由对应的权限码
 *  title  页面名（ this.pageTitle 默认取这里的值来显示 ）
 *  requiredId    true: 如果路由参数中缺少id，弹出warning，并且返回上一级页面
 *  confirmLeave  true: 离开页面前弹出是否离开的提示消息
 */
export default [
    // ------------------ 有增删改查功能的列表页面 ------------------
    {
      path: '/car-relation/list',
      name: 'car-relation-list',
      meta: {
        auth: 'ivsmp.car-relation.list',
        title: '多车关连',
        requiredId: false,
      },
      component: () => import('@/views/safetyDiagnosis/car-relation')
    },
    {
      path: '/car-relation/add',
      name: 'car-relation-add',
      meta: {
        auth: 'ivsmp.car-relation.add',
        title: '添加',
        confirmLeave: true,
      },
      component: () => import('@/views/safetyDiagnosis/car-relation/operate')
    },
    {
      path: '/car-relation/edit',
      name: 'car-relation-edit',
      meta: {
        auth: 'ivsmp.car-relation.edit',
        title: '编辑',
        confirmLeave: false,
      },
      component: () => import('@/views/safetyDiagnosis/car-relation/operate')
    },
    {
      path: '/car-relation/detail',
      name: 'car-relation-detail',
      meta: {
        auth: 'ivsmp.car-relation.detail',
        title: '查看',
        confirmLeave: false
      },
      component: () => import('@/views/safetyDiagnosis/car-relation/operate')
    }
];
