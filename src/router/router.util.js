import store from '@/store/index';
import util from '@/libs/util';
import { pathInit } from '@/store/modules/admin/modules/db';
import Setting from '@/config/setting';

const key = pathInit({
  dbName: 'sys',
  path: 'page.opened',
  defaultValue: {},
  user: true,
});

async function setSystemStatus(to) {
  // 加载用户信息
  if (!Object.keys(store.state.admin.user.info).length) {
    // util.loading.on();
    await store.dispatch('admin/account/login');
  }
  // 系统初始化
  if (!store.state.admin.system.initialized) {
    await store.dispatch('admin/system/initSystem');
  }

  // 路由改变，设置相关菜单的状态
  // await store.dispatch('admin/system/watchRoute', to);
}

function checkRouteType(to) {
  // 判断目标路由的类型
  if (to.matched.length) {
    if (to.meta.frameOut) {
      store.commit('admin/page/setPageType', 'frameOut');
    } else {
      store.commit('admin/page/setPageType', 'frameIn');
    }
  } else {
    store.commit('admin/page/setPageType', 'noMatch');
  }
}
function handleSpecialRoute(to, from) {
  if (Setting.layout.headerMenu) {
    if (['personal', 'home'].includes(to.name)) {
      hide();
    }
    if (['personal', 'home'].includes(from.name)) {
      show();
    }
  }
  if (to.name === 'login') {
    util.cookies.remove('token');
    sessionStorage.removeItem(key);
    store.dispatch('admin/user/set', {}, { root: true });
    store.commit('admin/system/setInitialized', false, { root: true });
  }
}

function hide() {
  setTimeout(() => {
    util.layout.hideSider(true);
    util.layout.hideBreadcrumb(true);
    util.layout.setHeader();
  });
}

function show() {
  util.layout.hideSider(false);
  util.layout.hideBreadcrumb(false);
}

export { setSystemStatus, checkRouteType, handleSpecialRoute };
