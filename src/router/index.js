import { createRouter, createWebHistory } from 'vue-router';
import ViewUIPlus from 'view-ui-plus';
import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper';

import routes from './routes';
import { checkRouteType, handleSpecialRoute, setSystemStatus } from './router.util';
import util from '@/libs/util';
import { sleep } from '@/libs/time';

import Setting from '@/config/setting';

import store from '@/store/index';

const router = createRouter({
  routes,
  history: createWebHistory(qiankunWindow.__POWERED_BY_QIANKUN__ ? Setting.qiankunMainRoute + Setting.qiankunRouterBase : Setting.routerBase),
  scrollBehavior() {
    return { top: 0 };
  },
});

/**
 * 路由拦截
 * 权限验证
 */
router.beforeEach(async (to, from, next) => {
  store.state.admin.waringlist.previousRoutePath = from.path;
  const confirmLeavePromise = (args = {}) => {
    return new Promise(async (resolve, reject) => {
      const {
        onOK = () => {
          return new Promise(async (resolve, reject) => {
            resolve();
          });
        },
      } = args;
      if (from.meta && from.meta.confirmLeave && !(to.params && (to.params.skipConfirmLeave === 'true' || to.params.skipConfirmLeave === true))) {
        window.$vuePrototype.$confirmCancel({
          title: '提示',
          content: '是否确定放弃当前录入的信息？',
          onOk: async () => {
            store.dispatch('admin/pageData/savePageData', { to, from });
            await onOK();
            resolve(true);
          },
          onCancel: async () => {
            resolve(false);
          },
        });
      } else {
        store.dispatch('admin/pageData/savePageData', { to, from });
        await onOK();
        resolve(true);
      }
    });
  };

  // 微前端环境下判断是否有当前路由的权限，没有调基座的403处理
  if (qiankunWindow.__POWERED_BY_QIANKUN__) {
    if (to.fullPath === history.state.current) {
      // [MEMO VU3升级] 可能是因为基座和子系统用的 vue-router 版本不一致。子系统升级到 VUE3 后，通过 push 调整路由，会触发两次 beforeEach，第二次的时候 params 为空。暂时没有找到治本的方案，这里通过延长第二次的执行时间来确保页面能取到 params
      await sleep(1);
      next();
    } else if (to.matched.some((_) => _.meta.auth)) {
      const isPermission = window.$vuePrototype.$auth(to.meta.auth);
      if (isPermission) {
        const isLeave = await confirmLeavePromise();
        if (isLeave) {
          next();
        } else {
          next(false);
        }
      } else {
        // 基座传给子应用的方法，挂在到vue的原型上了
        window.$vuePrototype.$error(403);
      }
    } else {
      const isLeave = await confirmLeavePromise();
      if (isLeave) {
        next();
      } else {
        next(false);
      }
    }
  } else {
    if (Setting.showProgressBar) {
      ViewUIPlus.LoadingBar.start();
    }
    // 判断路由类型
    // checkRouteType(to);
    if (to.matched.some((_) => _.meta.auth)) {
      // 这里依据 token 判断是否登录，可视情况修改
      await store.dispatch('admin/account/checkLoginStatus', to);
      // 初始化系统数据
      await setSystemStatus(to);
      // 路由鉴权
      const isPermission = util.auth.has(to.meta.auth);
      if (isPermission) {
        const isLeave = await confirmLeavePromise({
          onOK: () => {
            return new Promise(async (resolve, reject) => {
              try {
                // 路由改变，设置相关菜单的状态
                await store.dispatch('admin/system/watchRoute', to);
                resolve();
              } catch (error) {
                resolve();
              }
            });
          },
        });
        if (isLeave) {
          next();
        } else {
          next(false);
        }
      } else {
        next({
          name: '403',
        });
      }
    } else {
      // 不需要身份校验 直接通过
      const isLeave = await confirmLeavePromise();
      if (isLeave) {
        next();
      } else {
        next(false);
      }
    }
    // 处理个人中心等路由，手动隐藏侧边栏
    handleSpecialRoute(to, from);
  }
});

router.afterEach((to) => {
  if (Setting.showProgressBar) {
    ViewUIPlus.LoadingBar.finish();
  }
  if (qiankunWindow.__POWERED_BY_QIANKUN__) {
    const route = Object.assign({}, to);
    // 更新基座的opened页面
    route.meta.from = 'child';
    route.path = `${Setting.qiankunRouterBase}${route.path}`;
    route.fullPath = `${Setting.qiankunRouterBase}${route.fullPath}`;
    // Vue.$page.open(route);
  } else {
    // 多页控制 打开新的页面
    location.href.includes('ticket=')
      ? window.history.replaceState({}, '', util.cas.removeTicket())
      : util.cookies.get('token') && store.dispatch('admin/page/open', to);
  }
  // 返回页面顶端
  const dom = document.querySelector('.i-layout-content-main');
  dom && dom.scrollTo(0, 0);
  // window.scrollTo(0, 0);
});

export default router;
