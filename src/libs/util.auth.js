import store from '@/store/index';

const auth = {};
/**
 * @description: 根据权限码判断是否有相关权限
 * @param {String | Array} authCode 权限码（集合）
 * @return {<PERSON>llean} true 有权限 | false 无权限
 */
auth.has = function (authCode) {
  if (typeof authCode === 'boolean') {
    return authCode;
  }

  authCode = Array.isArray(authCode) ? authCode : [authCode];
  for (let i = 0; i < authCode.length; i++) {
    if (store.state.admin.user.info.access.includes(authCode[i])) {
      return true;
    }
  }

  return false;
};

export default auth;
