import _, { random as randomLodash } from 'lodash';

export function cloneDeep(...args) {
  return _.cloneDeep(...args);
}

export function merge(...args) {
  return _.merge(...args);
}

export function isFunction(...args) {
  return _.isFunction(...args);
}

export function isEqual(...args) {
  return _.isEqual(...args);
}

export const throttle = (...args) => {
  return _.throttle(...args);
};

export const random = (...args) => {
  return randomLodash(...args);
};

// 生成唯一标识
export function createUUID() {
  var rdm = randomFloat();
  return new Date().getTime() + rdm.toString(36);
}

// 为了修复 Insecure Randomness，替代 Math.random()
export function randomFloat() {
  const fooArray = new Uint32Array(1);
  const maxUint32 = 0xffffffff;
  const rdm = crypto.getRandomValues(fooArray)[0] / maxUint32;
  return rdm;
}

/* ================================= Array ================================= */

export function isArray(...args) {
  return _.isArray(...args);
}

export function take(...args) {
  return _.take(...args);
}

export function drop(...args) {
  return _.drop(...args);
}

export function remove(...args) {
  return _.remove(...args);
}

export function differenceBy(...args) {
  return _.differenceBy(...args);
}

export function pullAllBy(...args) {
  return _.pullAllBy(...args);
}

export function findRight(ary = [], isMatch = () => false) {
  let _elem;
  for (let i = ary.length - 1; i >= 0; i--) {
    const elem = ary[i];
    if (isMatch(elem)) {
      _elem = elem;
      break;
    }
  }

  return _elem;
}

export function removeRight(ary = [], isMatch = () => false) {
  let index;
  for (let i = ary.length - 1; i >= 0; i--) {
    const elem = ary[i];
    if (isMatch(elem)) {
      index = i;
      break;
    }
  }

  if (index !== undefined) {
    ary.splice(index, 1);
  }
}

export function upAryItem(ary = [], curIndex) {
  if (curIndex === 0) {
    return curIndex;
  }
  const nextIndex = curIndex - 1;
  ary[curIndex] = ary.splice(nextIndex, 1, ary[curIndex])[0];
  return nextIndex;
}

export function downAryItem(ary = [], curIndex) {
  if (curIndex > ary.length - 1) {
    return curIndex;
  }
  const nextIndex = curIndex + 1;
  ary[curIndex] = ary.splice(nextIndex, 1, ary[curIndex])[0];
  return nextIndex;
}

// 判断数组中是否有重复的某个值
export function hasSameValue(ary = [], val) {
  let num = 0;
  for (let i = 0; i < ary.length; i++) {
    if (ary[i] === val) {
      num++;
    }
    if (num > 1) {
      break;
    }
  }

  return num > 1;
}

/* ================================= Collection ================================= */

export function sortBy(...args) {
  return _.sortBy(...args);
}

export function orderBy(...args) {
  return _.orderBy(...args);
}

export function some(...args) {
  return _.some(...args);
}

export function find(...args) {
  return _.find(...args);
}

export function forEach(...args) {
  return _.forEach(...args);
}

/* ================================= Number ================================= */

// 向上取整
export function ceil(...args) {
  return _.ceil(...args);
}

// 四舍五入
export function round(...args) {
  return _.round(...args);
}

// 向下取整
export function floor(...args) {
  return _.floor(...args);
}

export function isNonNullNumber(value) {
  if (value === undefined || value === null || value === '' || (value.trim && value.trim() === '')) {
    return false;
  }
  if (!isNaN(_.toNumber(value))) {
    return true;
  } else {
    return false;
  }
}
