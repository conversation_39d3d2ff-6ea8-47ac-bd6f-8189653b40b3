import store from '@/store/index';
// import router from '@/router';

function error(status) {
  switch (status) {
    case 401:
      handle401();
      break;
    //  暂不额外处理其他情况
    // case 403:
    //     handle403();
    //     break;
    // case 404:
    //     handle404();
    //     break;
    default:
      // alert('无法处理的错误：' + status)
      break;
  }
}

function handle401() {
  // 未授权
  store.dispatch('admin/account/handleUnauthorized');
}

// function handle403 () {
//   // 未授权
//   router.push({ name: '403' })
// }

// function handle404 () {
//   // 未授权
//   router.push({ name: '404' })
// }

export default error;
