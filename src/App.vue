<template>
  <div id="app">
    <template v-if="pageType === 'frameOut'">
      <router-view />
    </template>
    <layout v-else />
    <!---全局未授权（401）弹窗-->
    <Modal v-model="unauthorizedModal" :closable="true" :mask-closable="false" :styles="{ top: '30vh' }">
      <template v-slot:header>
        <p style="text-align: center">
          <Icon type="ios-information-circle" color="#f60" size="20" />
          <span style="margin-left: 4px; font-size: 18px; font-weight: bold">未授权</span>
        </p>
      </template>
      <div style="text-align: center; font-size: 16px; margin: 28px 0">
        <p>当前身份凭证已失效，需重新进行授权认证！</p>
      </div>
      <template v-slot:footer>
        <Button type="primary" size="large" long @click="handleReauthorization">前往授权</Button>
      </template>
    </Modal>
  </div>
</template>

<script>
import { off, on } from 'view-ui-plus/src/utils/dom';
import { setMatchMedia } from 'view-ui-plus/src/utils/assist';
import { mapActions, mapMutations, mapState } from 'vuex';
import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper';
import Layout from './layouts/basic-layout/index';

setMatchMedia();

export default {
  name: 'App',
  components: {
    Layout,
  },
  computed: {
    ...mapState('admin/page', ['type']),
    ...mapState('admin/system', ['unauthorizedConfirm']),
    unauthorizedModal: {
      get() {
        return this.unauthorizedConfirm;
      },
      set(newValue) {
        this.$store.commit('admin/system/setUnauthorizedConfirm', false);
      },
    },
    pageType: function () {
      if (qiankunWindow.__POWERED_BY_QIANKUN__) {
        return this.type;
      } else {
        let isFrameOut = false;
        if (qiankunWindow.$isFrameOutAll) {
          isFrameOut = true;
        } else if (this.$route.query) {
          if (this.$route.query && this.$route.query.fo === '1') {
            isFrameOut = true;
          }
        }
        if (isFrameOut || this.$route.fullPath === '/' || (this.$route.meta && this.$route.meta.frameOut)) {
          return 'frameOut';
        } else {
          return 'frameIn';
        }
      }
    },
  },
  mounted() {
    on(window, 'resize', this.handleWindowResize);
    this.handleMatchMedia();
  },
  beforeUnmount() {
    off(window, 'resize', this.handleWindowResize);
  },
  methods: {
    ...mapMutations('admin/layout', ['setDevice']),
    ...mapActions('admin/account', ['handleReauthorization']),
    handleWindowResize() {
      this.handleMatchMedia();
    },
    handleMatchMedia() {
      const matchMedia = window.matchMedia;

      if (matchMedia('(max-width: 600px)').matches) {
        this.setDevice('Mobile');
      } else if (matchMedia('(max-width: 992px)').matches) {
        this.setDevice('Tablet');
      } else {
        this.setDevice('Desktop');
      }
    },
  },
};
</script>
