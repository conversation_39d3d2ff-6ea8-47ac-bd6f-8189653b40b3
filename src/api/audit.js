import request from '@/plugins/request';
import API from '@/api/config/index';
import Mock from 'mockjs';
import { urlGetParams, getUrlQueryParametere } from '@/libs/url';

// ------------------ 流量商品管理 ------------------

export function getTableList(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/car-audits/pages`,
        method: 'get',
        params: data,
    });
}

export function getDetail(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/car-audits/${data.id}`,
        method: 'get',
        // params: data,
    });
}
