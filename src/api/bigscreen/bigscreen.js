import request from '@/plugins/request';
import API from '@/api/config/index';

export function getStatisticData(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/statisticData`,
        method: 'get',
        params: data,
    });
}

export function getWxdjtjData() {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/wxdjtjData`,
        method: 'get'
    });
}

export function getBrandStatisticData() {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/brandStatisticData`,
        method: 'get'
    });
}

export function getGjlxTopFiveData() {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/gjlxTopFiveData`,
        method: 'get'
    });
}

export function getLbjTopFiveData() {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/lbjTopFiveData`,
        method: 'get'
    });
}

export function getGjqsLineData() {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/gjqsLineData`,
        method: 'get'
    });
}

export function getBgjsfTopFiveData() {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/bgjsfTopFiveData`,
        method: 'get'
    });
}

export function getAttackListData(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/attackListData`,
        method: 'get',
        params: data
    });
}

export function getCarPartAttackData(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/carPartAttackData`,
        method: 'get',
        params: data
    });
}

export function getXmclfbData(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/xmclfbData`,
        method: 'get',
        params: data
    });
}

export function getXmsjfbData(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/xmsjfbData`,
        method: 'get',
        params: data
    });
}

export function getLbjqsLineData(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/lbjqsLineData`,
        method: 'get',
        params: data
    });
}

export function getWxdjfbData(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/wxdjfbData`,
        method: 'get',
        params: data
    });
}

export function getCarModelData() {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/carModelData`,
        method: 'get'
    });
}

export function getAttackMapData() {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/bigScreen/attackMapData`,
        method: 'get'
    });
}
