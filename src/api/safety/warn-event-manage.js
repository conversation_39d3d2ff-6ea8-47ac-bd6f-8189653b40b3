import request from '@/plugins/request';
import API from '@/api/config/index';
import Mock from 'mockjs';
import { urlGetParams, getUrlQueryParametere } from '@/libs/url';

// ------------------ 流量商品管理 ------------------

export function getTableList1(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/alarm-infos/source/pages`,
        method: 'get',
        params: data,
    });
}

export function getTableList2(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/alarm-infos/many/pages`,
        method: 'get',
        params: data,
    });
}

export function getTableList3(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/alarm-infos/multiple/pages`,
        method: 'get',
        params: data,
    });
}

export function relationItem(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/alarm-events/pages`,
        method: 'get',
        params: data,
    });
}

export function add(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/alarm-infos/manual-operation`,
        method: 'post',
        data,
    });
}

export function dealApi(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/alarm-infos/change-status`,
        method: 'put',
        data,
    });
}

export function getDetailInfo(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/alarm-infos/${data.id}`,
        method: 'get',
    });
}
