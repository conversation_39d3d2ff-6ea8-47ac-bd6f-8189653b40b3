import request from '@/plugins/request';
import API from '@/api/config/index';
import Mock from 'mockjs';
import { urlGetParams, getUrlQueryParametere } from '@/libs/url';

// ------------------ 流量商品管理 ------------------

export function getTableList(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/workflow-alarm-orders/pages`,
        method: 'get',
        params: data,
    });
}

export function getFlowList(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/workflow-processs/pages`,
        method: 'get',
        params: data,
    });
}

export function getDetail(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/workflow-alarm-orders/${data.id}`,
        method: 'get',
        params: data,
    });
}

export function distribute(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/workflow-orders/distribute`,
        method: 'put',
        data,
    });
}

export function judge(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/workflow-orders/judge`,
        method: 'put',
        data,
    });
}
