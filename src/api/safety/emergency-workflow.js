import request from '@/plugins/request';
import API from '@/api/config/index';
import Mock from 'mockjs';
import { urlGetParams, getUrlQueryParametere } from '@/libs/url';

// ------------------ 流量商品管理 ------------------

export function getTableList(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/workflow-configs/pages`,
        method: 'get',
        params: data,
    });
}

export function getDetail(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/workflow-configs/${data.id}`,
        method: 'get',
        params: data,
    });
}

export function updateNode(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/workflow-configs/update-node`,
        method: 'put',
        data,
    });
}
