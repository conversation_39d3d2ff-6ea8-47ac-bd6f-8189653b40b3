import request from '@/plugins/request';
import API from '@/api/config/index';
import Mock from 'mockjs';
import { urlGetParams, getUrlQueryParametere } from '@/libs/url';

// ------------------ 流量商品管理 ------------------

export function getmgwordList(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/mgword/list`,
        method: 'get',
        params: data,
    });
}

export function deletemgwordItems({ ids }) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/mgword`,
        method: 'delete',
        data: ids
    });
}

export function getmgwordDetail(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/mgword`,
        method: 'get',
        params: data,
    });
}

export function addmgwordItem(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/mgword`,
        method: 'post',
        data,
    });
}

export function editmgwordItem(data) {
    return request({
        url: `${API.authAPIUrl}${API.prefixAPI}/mgword`,
        method: 'put',
        data,
    });
}

Mock.mock(urlGetParams('/mock/api/mgword/list'), 'get', (options) => {
    // const requestData = JSON.parse(options.body);
    const requestData = getUrlQueryParametere(options.url);
    console.log('[MOCK] getmgwordList requestData', requestData);

    const total = 66;
    const size = 10;

    const dataList = Mock.mock({
        [`records|${size}`]: [
            {
                'id|+1': 1,
                'packageName|1': ['电信全国50M流量包', '电信全国流量直充包', '电信流量日包', '电信全国50M流量包', '电信全国流量直充包'],
                carCompany: '一汽汽车',
                'packageTypeName|1': ['月包', '年包', '日包'],
                flowType: '130701', // 通用流量
                flowTypeName: '通用流量',
                price: '@float(1,999,0,2)',
                bestPrice: '@float(1,999,0,2)',
                'flowTotal|1': ['50M', '通用50M+视频50M+音乐50M'],
                createTime: '@datetime',
                updateTime: '@datetime',
            },
        ],
        total,
    });

    dataList.records[1].flowType = '130703'; // 定向流量
    dataList.records[1].flowTypeName = '定向流量'; // 定向流量

    dataList.records[2].flowType = '130702'; // 组合流量
    dataList.records[1].flowTypeName = '定向流量'; // 组合流量

    return {
        code: 0,
        data: dataList,
    };
});

Mock.mock('/mock/api/mgword', 'delete', (options) => {
    const requestData = JSON.parse(options.body);
    console.log('[MOCK] deletemgwordItems requestData', requestData);

    return {
        code: 0,
        data: {},
    };
});

Mock.mock(urlGetParams('/mock/api/mgword'), 'get', (options) => {
    // const requestData = JSON.parse(options.body);
    const requestData = getUrlQueryParametere(options.url);
    console.log('[MOCK] getmgwordDetail requestData', requestData);

    /*
    const dataDetail = Mock.mock({
      ['id|+1']: 1,
      'packageName|1': ['电信全国50M流量包', '电信全国流量直充包', '电信流量日包', '电信全国50M流量包', '电信全国流量直充包'],
      carCompany: '一汽汽车',
      'packageType|1': [1, 2, 3],
      'flowType|1': [1, 2, 3],
      flowTotal: '@float(1,100,2,3)',
      flowUnitType: 'GB',
      // packageIcon
      memo: 'abc',
      price: '@float(1,100,2,3)',
      bestPrice: '@float(1,100,2,3)',
      packageEffect: 12,
      exclusionDetail: 1,
      isCarry: 22,
      carCompany: [11, 22],
    });
    */

    let dataDetail = {
        id: requestData.id,
        packageName: '电信全国50M流量包',
        packageType: '130103', // 年包
        flowType: '130701', // 通用流量
        memo: 'abc',
        price: 100,
        bestPrice: 90,
    };

    switch (requestData.packageId) {
        case '2':
            dataDetail.packageType = '130102'; // 月包
            dataDetail.flowType = '130703'; // 定向流量
            break;
        case '3':
            dataDetail.packageType = '130101'; // 日包
            dataDetail.flowType = '130702'; // 组合流量
            break;
        case '1':
        default:
            break;
    }

    console.log('[MOCK] getmgwordDetail responseData', dataDetail);
    return {
        code: 0,
        data: dataDetail,
    };
});

Mock.mock('/mock/api/mgword', 'post', (options) => {
    const requestData = JSON.parse(options.body);
    console.log('[MOCK] addmgwordItem requestData', requestData);

    return {
        code: 0,
        data: {},
    };
});

Mock.mock('/mock/api/mgword', 'put', (options) => {
    const requestData = JSON.parse(options.body);
    console.log('[MOCK] editmgwordItem requestData', requestData);

    return {
        code: 0,
        data: {},
        msg: '',
    };
});
