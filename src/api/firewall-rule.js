import API from '@/api/config/index';
import request from '@/plugins/request';

// ------------------ 入侵规则检测 ------------------

const prefix = `${API.authAPIUrl}${API.prefixAPI}`;
// const prefix = `${API.authAPIUrl}/inner`;

export function getFirewallRuleList(data) {
  return request({
    url: `${prefix}/strategy-firewall-rules/pages`,
    method: 'get',
    params: data,
  });
}

export function getFirewallRuleDetail(data) {
  return request({
    url: `${prefix}/strategy-firewall-rules/${data.id}`,
    method: 'get',
  });
}

export function createFirewallRule(data) {
  return request({
    url: `${prefix}/strategy-firewall-rules`,
    method: 'post',
    data,
  });
}

export function publishFirewallRule(data) {
  return request({
    url: `${prefix}/strategy-firewall-rules/publish`,
    method: 'post',
    data,
  });
}

export function updateFirewallRule(data) {
  return request({
    url: `${prefix}/strategy-firewall-rules`,
    method: 'put',
    data,
  });
}

export function deleteFirewallRule(data) {
  return request({
    url: `${prefix}/strategy-firewall-rules/${data.id}`,
    method: 'delete',
  });
}
