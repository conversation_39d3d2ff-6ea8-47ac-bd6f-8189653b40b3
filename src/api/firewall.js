import API from '@/api/config/index';
import request from '@/plugins/request';

// ------------------ 入侵规则检测 ------------------

const prefix = `${API.authAPIUrl}${API.prefixAPI}`;
// const prefix = `${API.authAPIUrl}/inner`;

export function getFirewallList(data) {
  return request({
    url: `${prefix}/strategy-firewalls/pages`,
    method: 'get',
    params: data,
  });
}

export function getFirewallDetail(data) {
  return request({
    url: `${prefix}/strategy-firewalls/${data.id}`,
    method: 'get',
  });
}

export function createFirewall(data) {
  return request({
    url: `${prefix}/strategy-firewalls`,
    method: 'post',
    data,
  });
}

export function copyFirewall(data) {
  return request({
    url: `${prefix}/strategy-firewalls/copy`,
    method: 'post',
    data,
  });
}

export function updateFirewall(data) {
  return request({
    url: `${prefix}/strategy-firewalls`,
    method: 'put',
    data,
  });
}

export function deleteFirewall(data) {
  return request({
    url: `${prefix}/strategy-firewalls/${data.id}`,
    method: 'delete',
  });
}
