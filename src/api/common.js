import request from '@/plugins/request';
import API from '@/api/config/index';

// ------------------ 字典 ------------------

export function getDicData({ dicType }) {
  return request({
    // url: `${API.authAPIUrlBase}${API.prefixAPIBase}/biz-dicts/${dicType}/dict/enable`,
    url: `http://192.168.6.228:9004/doiov-system-auth${API.prefixAPIBase}/biz-dicts/${dicType}/dict/enable`,
    // url: `http://47.120.0.242:8888/doiov-system-auth/api/biz-dicts/${dicType}/dict/enable`,
    method: 'get',
  });
}
