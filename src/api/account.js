import request from '@/plugins/request';
import API from '@/api/config/index';
// cas登录时，通过ticket拿token
export function AccountToken(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/system/loginByCas`,
    method: 'post',
    data,
  });
}
// 自定义登录接口，可修改
export function AccountLogin(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/system/loginByCustome`,
    method: 'post',
    data,
  });
}
