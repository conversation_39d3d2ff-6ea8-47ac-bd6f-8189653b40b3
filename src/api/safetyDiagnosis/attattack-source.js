import request from '@/plugins/request';
import API from '@/api/config/index';

// 获取列表
export function getListApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/security-rules/pages`,
    method: 'get',
    params: data
  });
}

// 新增
export function addApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/security-rules`,
    method: 'post',
    data
  });
}

// 编辑
export function editApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/security-rules`,
    method: 'put',
    data
  });
}

// 删除
export function deleteApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/security-rules/${data.id}`,
    method: 'delete'
  });
}

// 查看
export function detailApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/security-rules/${data.id}`,
    method: 'get'
  });
}

// 查看事件
export function getRulesApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/strategy-snort-rules/conditions`,
    method: 'get'
  });
}
