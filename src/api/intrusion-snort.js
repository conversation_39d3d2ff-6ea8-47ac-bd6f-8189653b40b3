import API from '@/api/config/index';
import request from '@/plugins/request';

// ------------------ 入侵规则检测 ------------------

const prefix = `${API.authAPIUrl}${API.prefixAPI}`;
// const prefix = `${API.authAPIUrl}/inner`;

export function getIntrusionSnortList(data) {
  return request({
    url: `${prefix}/strategy-snorts/pages`,
    method: 'get',
    params: data,
  });
}

export function getIntrusionSnortDetail(data) {
  return request({
    url: `${prefix}/strategy-snorts/${data.id}`,
    method: 'get',
  });
}

export function createIntrusionSnort(data) {
  return request({
    url: `${prefix}/strategy-snorts`,
    method: 'post',
    data,
  });
}

export function copyIntrusionSnort(data) {
  return request({
    url: `${prefix}/strategy-snorts/copy`,
    method: 'post',
    data,
  });
}

export function updateIntrusionSnort(data) {
  return request({
    url: `${prefix}/strategy-snorts`,
    method: 'put',
    data,
  });
}

export function deleteIntrusionSnort(data) {
  return request({
    url: `${prefix}/strategy-snorts/${data.id}`,
    method: 'delete',
  });
}

// 按条件查询
export function getAllSnortRules(data) {
  return request({
    url: `${prefix}/strategy-snort-rules/conditions`,
    method: 'get',
    params: data,
  });
}
