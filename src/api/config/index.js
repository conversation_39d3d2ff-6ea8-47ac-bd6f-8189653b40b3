import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper';
import URL_DEV from './url.dev';
import URL_MK from './url.mockdata';
import URL_TEST from './url.test';
import URL_PRO from './url.pro';

const env = process.env.NODE_ENV;

let APIs = '';
switch (env) {
  case 'development':
    APIs = URL_DEV;
    break;
  case 'production':
    APIs = URL_PRO;
    break;
  default:
    APIs = URL_TEST;
    break;
}

const API = APIs;
// 加载外挂文件的配置
Object.keys(API).forEach((key) => {
  if (qiankunWindow[`$${key}`]) {
    API[key] = qiankunWindow[`$${key}`];
  }
});

// 使用 npm run mk 启动的时候，页面使用本地 mock 数据
if (process.env.VUE_APP_ENV === 'mockdata') {
  API.baseUrl = URL_MK.baseUrl;
  API.authAPIUrl = URL_MK.authAPIUrl;
  API.prefixAPI = URL_MK.prefixAPI;
  API.authAPIUrlBase = URL_MK.authAPIUrlBase;
  API.prefixAPIBase = URL_MK.prefixAPIBase;
}

export default API;
