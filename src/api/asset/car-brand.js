import request from '@/plugins/request';
import API from '@/api/config/index';

// 获取品牌列表
export function getListApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-brands/pages`,
    method: 'get',
    params: data
  });
}

// 新增品牌
export function addApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-brands`,
    method: 'post',
    data
  });
}

// 编辑品牌
export function editApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-brands`,
    method: 'put',
    data
  });
}

// 删除品牌
export function deleteApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-brands/${data.id}`,
    method: 'delete'
  });
}

// 查看品牌
export function detailApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-brands/${data.id}`,
    method: 'get'
  });
}

// 按条件查询所有品牌
export function getAllBrandApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-brands/conditions`,
    method: 'get',
    params: data
  });
}
