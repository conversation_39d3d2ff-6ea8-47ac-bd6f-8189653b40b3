import request from '@/plugins/request';
import API from '@/api/config/index';

// 获取列表
export function getListApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-models/pages`,
    method: 'get',
    params: data,
  });
}

// 新增
export function addApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-models`,
    method: 'post',
    data
  });
}

// 删除
export function deleteApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-models/${data.id}`,
    method: 'delete'
  });
}

// 查看
export function detailApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-models/${data.id}`,
    method: 'get'
  });
}

// 编辑
export function editApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-models`,
    method: 'put',
    data,
  });
}

// 按条件查询
export function getAllModelsApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-models/conditions`,
    method: 'get',
    params: data
  });
}
