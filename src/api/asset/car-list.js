import request from '@/plugins/request';
import API from '@/api/config/index';

// 获取列表
export function getListApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-cars/pages`,
    method: 'get',
    params: data
  });
}

// 新增
export function addApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-cars`,
    method: 'post',
    data
  });
}

// 编辑
export function editApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-cars`,
    method: 'put',
    data
  });
}

// 删除
export function deleteApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-cars/${data.id}`,
    method: 'delete'
  });
}

// 查看
export function detailApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-cars/${data.id}`,
    method: 'get'
  });
}

// 按条件查询
export function getAllModelsApi(data) {
  return request({
    url: `${API.authAPIUrl}${API.prefixAPI}/car-cars/conditions`,
    method: 'get',
    params: data
  });
}

// 导入
export const importApi = `${API.baseUrl}${API.authAPIUrl}${API.prefixAPI}/car-cars/import`;
