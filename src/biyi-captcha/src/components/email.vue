<template>
  <div>
    <Modal v-model="show" width="600px" :mask-closable="false" title="邮箱验证">
      <Form ref="formEmail" :model="formEmail" :rules="emailRule" @submit.prevent>
        <FormItem prop="code">
          <Input type="text" v-model="formEmail.code" placeholder="请输入邮箱验证码" @on-enter="submitEmailCode">
            <template #append>
              <Button :disabled="disabled" class="countDowm" @click="getEmailCode" type="primary" :loading="loadingBtn">
                <span class="contentText" v-if="!disabled">点击获取验证码</span>
                <span class="contentText" v-else>重新获取({{ count }})</span>
              </Button>
            </template>
          </Input>
        </FormItem>
      </Form>
      <template #close>
        <Icon @click="choseCaptcha" style="margin: 8px 8px 0 0" color="#45494c" size="18" type="ios-close-circle" />
      </template>
      <template #footer>
        <Button type="primary" @click="submitEmailCode()">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<script>
import service from '../../request/index.js';
export default {
  name: 'EmailCatpcha',

  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    email: {
      type: String,
      default: '',
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      formEmail: {
        code: '',
      },
      disabled: false,
      count: 60,
      loadingBtn: false,
      emailRule: {
        code: [{ required: true, message: '验证码不能为空', trigger: 'blur' }],
      },
    };
  },

  computed: {
    show: {
      get() {
        return this.modelValue;
      },
      set() {},
    },
  },

  methods: {
    /**
     * @description: 获取邮箱验证码
     */
    getEmailCode() {
      this.loadingBtn = true;
      let data = {
        key: this.email,
      };
      service('/api/captcha/email-captcha', { params: data })
        .then((res) => {
          this.$Message.success(res.msg || '发送成功，请登录邮箱查看');
          this.setCountDown();
        })
        .catch((err) => {
          this.$Message.error(err.response.data && err.response.data.message);
        })
        .finally(() => {
          this.loadingBtn = false;
        });
    },
    setCountDown() {
      this.count = 60;
      this.disabled = true;
      let timer = setInterval(() => {
        if (this.count > 0) {
          this.count--;
        } else {
          this.disabled = false;
          clearInterval(timer);
        }
      }, 1000);
    },
    /**
     * @description: 关闭验证码弹窗，并向父组件传递值
     */
    choseCaptcha() {
      this.$emit('choseCaptcha', false);
      this.$refs['formEmail'].resetFields();
    },
    /**
     * @description: 校验通过后，向父组件传递验证数据
     */
    submitEmailCode() {
      this.$refs.formEmail.validate((valid) => {
        if (valid) {
          const data = {
            key: this.email,
            value: this.formEmail.code,
          };
          this.$emit('getVerifyRes', data);
          this.$refs['formEmail'].resetFields();
        }
      });
    },
  },
};
</script>

<style scoped>
.contentText {
  width: 100px;
  display: inline-block;
  text-align: center;
}
</style>
