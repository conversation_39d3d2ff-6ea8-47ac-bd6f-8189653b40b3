<template>
  <div :style="{ width: `${width}px`, position: 'relative' }">
    <div class="pop" v-if="show">
      <div class="verifybox">
        <div class="verifybox-top">
          请完成安全验证
          <span class="verifybox-close">
            <Icon @click="choseCaptcha" type="ios-close-circle" />
          </span>
        </div>
        <div class="verifybox-bottom">
          <div style="position: relative">
            <div class="verify-img-out">
              <div class="verify-img-panel">
                <div class="verify-refresh">
                  <Icon type="md-refresh" @click.self="getImg" style="color: #fff; font-size: 22px" />
                </div>
                <span class="verify-tips"></span>
                <img
                  :src="'data:image/png;base64,' + bgImg"
                  class="backImg"
                  style="width: 100%; height: 100%; display: block; cursor: pointer"
                  alt="验证码图片加载错误"
                  @click.self="getImg"
                  title="点击刷新验证码"
                />
              </div>
              <Spin size="large" fix :show="spinShow" v-if="spinShow"></Spin>
            </div>
            <div class="verify-bar-area">
              <span class="verify-msg" v-if="startPos === 0">向右滑动完成验证</span>
              <div class="verify-left-bar" ref="verifyLeftBar" :style="{ width: `${x}px` }">
                <span class="verify-msg" style="color: rgb(0, 0, 0)"></span>
                <div class="verify-move-block" ref="verifyMoveBlock" :style="{ left: `${x}px` }">
                  <i class="ivu-icon ivu-icon-ios-arrow-forward"></i>
                  <div class="verify-sub-block">
                    <img
                      :src="'data:image/png;base64,' + partImg"
                      class="bock-backImg"
                      alt="验证码图片加载错误"
                      title="点击刷新验证码"
                      @click.self="getImg"
                      style="width: 100%; height: 100%; display: block"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="mask" v-if="show"></div> -->
    </div>
  </div>
</template>

<script>
import service from '../../request/index.js';
import './verfy.css';
export default {
  name: 'DragCaptcha',

  props: {
    width: {
      type: [String, Number],
      default: '300',
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    baseUrl: {
      type: String,
      default: '',
    },
    saveStatus: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      imgUrl: '',
      securityCode: '',
      isDisabled: true,
      startPos: 0, // 鼠标点击的起始位置
      bgImg: '', // 背景图片路径
      partImg: '', // 滑动图片路径
      x: 0, // 滑动距离
      spinShow: false, // 图片加载动画,
      result: false, // 验证结果
      captchaKey: '', // 验证码唯一标识
      loading: false,
      loadingBtn: true,
    };
  },

  computed: {
    show: {
      get() {
        return this.modelValue;
      },
      set(val) {},
    },
  },

  watch: {
    modelValue: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.refresh();
        }
      },
      immediate: true,
    },
  },

  methods: {
    /**
     * @description: 根据saveStatus配置项和 result值决定是否保留验证码数据
     */
    refresh() {
      if (!this.saveStatus || !this.result) {
        this.getImg();
      }
    },
    /**
     * @description: 获取验证码，并为滑块绑定move事件
     */
    getImg() {
      this.spinShow = true;
      this.result = false;
      service
        .get('/api/captcha/drag-captcha')
        .then((res) => {
          this.$refs.verifyLeftBar.onmousedown = this.moveStart.bind(this);
          this.bgImg = res.data.originalImageBase64;
          this.partImg = res.data.jigsawImageBase64;
          this.captchaKey = res.data.captchaKey;
          this.spinShow = false;
          this.$nextTick(() => {
            this.initSlider();
          });
        })
        .catch((err) => {
          this.spinShow = false;
          this.$Message.error('图片获取失败');
        });
    },
    /**
     * @description: 鼠标按下
     * @param {object} e 鼠标按下时事件对象
     */
    moveStart(e) {
      if (e.button === 0) {
        this.startPos = e.clientX;
        document.onmousemove = this.move.bind(this);
        document.onmouseup = this.moveEnd.bind(this);
      }
    },
    /**
     * @description: 鼠标移动
     * @param {object} e 鼠标移动时事件对象
     */
    move(e) {
      e.preventDefault();
      let width = e.clientX - this.startPos;
      width = width > 0 ? width : 0;
      width = width > 368 ? 368 : width;
      // this.$refs.verifyLeftBar.style.width = `${width}px`;
      // this.$refs.verifyMoveBlock.style.left = `${width}px`;
      this.x = width;
    },
    /**
     * @description: 鼠标移动结束
     * @param {object} e 鼠标放开时事件对象
     */
    moveEnd(e) {
      document.onmousemove = null;
      document.onmouseup = null;
      this.$refs.verifyLeftBar.onmousedown = null;
      const data = {
        key: this.captchaKey,
        value: JSON.stringify({
          x: this.x,
          y: 5,
        }),
      };
      this.$emit('getVerifyRes', data);
      this.choseCaptcha();
    },
    /**
     * @description: 关闭验证码弹窗，并向父组件传递值
     */
    choseCaptcha() {
      this.$emit('choseCaptcha', false);
    },
    /**
     * @description: 初始化滑动校验参数
     */
    initSlider() {
      this.startPos = 0;
      this.x = 0;
      this.$refs.verifyLeftBar.style.width = `40px`;
      this.$refs.verifyMoveBlock.style.left = `0px`;
    },
  },
  emits: ['getVerifyRes', 'choseCaptcha'],
};
</script>
