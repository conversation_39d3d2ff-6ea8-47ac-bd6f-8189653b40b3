/*
  pageItemsConfig 中的 searchFormItem 说明：
    valuekey           必须。表单属性值（通常设置成跟后端接口的属性值一样）。
    label              可选。表单项的 label。省略的时候用 pageItemsConfig 的 key 值。
    initValue          可选。表单项的初期值
    spanCol            可选。表单项的 span。省略的时候，根据 SearchForm 组件的 colPerRow 自动计算。
    formItemType       可选。表单项的类型。省略的时候是 Input
    dicType            可选。表单项的类型是 Select，且 option 来自字典的时候需要设置该值。
    valuekeyBeginTime  可选。表单项的类型是日期选择的时，设置开始时间对应的后端接口属性值，默认是 beginTime。
    valuekeyEndTime    可选。表单项的类型是日期选择的时，设置结束时间对应的后端接口属性值，默认是 endTime。

  pageItemsConfig 中的 searchFormItem 示例：
    pageItemsConfig: {
      套餐名称: {
        searchFormItem: {
          valuekey: 'packageName',
        },
      },
      车企: {
        searchFormItem: {
          valuekey: 'carCompany',
          initValue: '一汽汽车',
        },
      },
      套餐类型: {
        searchFormItem: {
          valuekey: 'packageType',
          formItemType: 'Select',
          optionList: [
            {
              value: 1,
              label: '月包',
            },
            {
              value: 2,
              label: '年包',
            },
            {
              value: 3,
              label: '日包',
            },
          ],
        },
      },
      流量类型: {
        searchFormItem: {
          valuekey: 'flowType',
          formItemType: 'Select',
          dicType: 1307,
        },
      },
      规则触发时间: {
        searchFormItem: {
          valuekey: 'triggerTime',
          formItemType: 'DateTimeRange',
          valuekeyBeginTime: 'startTriggerTime',
          valuekeyEndTime: 'endTriggerTime',
          spanCol: 8,
        },
      },
      规则完成时间: {
        searchFormItem: {
          valuekey: 'completionTime',
          formItemType: 'DateRange',
          valuekeyBeginTime: 'startCompletionTime',
          valuekeyEndTime: 'endCompletionTime',
        },
      },
      // formItemType: 'DateRangeSeparate' 的 valuekey 用于前端内部的处理，跟后端接口无关
      账期: {
        searchFormItem: {
          label: '账单期间',
          valuekey: 'paymentDay',
          formItemType: 'DateRangeSeparate',
          valuekeyBeginTime: 'startPaymentDay',
          valuekeyEndTime: 'endPaymentDay',
          // 只检索月份
          datePickerType: 'month',
          datePickerFormat: 'yyyy-MM',
          initValue: {
            valueBegin: '2023-03',
            valueEnd: '2023-05',
          },
        },
      },
      规则触发时间: {
        searchFormItem: {
          valuekey: 'triggerTime',
          formItemType: 'DateRangeSeparate',
          valuekeyBeginTime: 'startTriggerTime',
          valuekeyEndTime: 'endTriggerTime',
          // 初始值: 近 30 天
          initValue: {
            valueBegin: beginDate,
            valueEnd: endDate,
          },
          // 只允许查询近半年的数据
          datePickerEarliest,
          // 时间跨度限定为 31 天
          datePickerRangeLimited: 31,
        },
        listColumn: {
          valuekey: 'triggerTime',
          width: 170,
        },
      },
      账期: {
        searchFormItem: {
          valuekey: 'paymentDay',
          formItemType: 'Date',
          datePickerType: 'month',
          datePickerFormat: 'yyyy-MM',
        },
      },
    },
*/

import { timestampToTime, timestampToMonth } from '@/libs/format.js';

export function createSearchFormlist({ pageItemsConfig }) {
  let params = {};

  Object.keys(pageItemsConfig).forEach((key) => {
    const config = pageItemsConfig[key];
    if (config.searchFormItem) {
      const {
        label,
        valuekey,
        formItemType,
        // dicType,
        placeholder,
        ...others
      } = config.searchFormItem;

      const _label = label || key;

      let placeholderPrefix = '';
      switch (formItemType) {
        case 'Select':
        case 'Cascader':
        case 'Date':
        case 'DateRange':
        case 'DateTimeRange':
          placeholderPrefix = '请选择';
          break;
        default:
          placeholderPrefix = '请输入';
          break;
      }
      // const _placeholder = placeholder || `${placeholderPrefix}${_label}`;
      const _placeholder = placeholder || placeholderPrefix;

      params[key] = {
        label: _label,
        prop: valuekey,
        formItemType,
        placeholder: _placeholder,
        ...others,
      };
    }
  });

  return params;
}

export function getSearchFormData({ searchFormItems, searchFormData }) {
  let params = {};

  Object.keys(searchFormItems).forEach((key) => {
    const config = searchFormItems[key];
    const { formItemType, prop } = config;
    const val = searchFormData[prop];

    try {
      if (formItemType === 'DateRange') {
        const { valuekeyBeginTime = 'beginTime', valuekeyEndTime = 'endTime' } = config;
        if (val && val[0]) {
          params[valuekeyBeginTime] = `${timestampToTime(val[0])} 00:00:00`;
        }
        if (val && val[1]) {
          params[valuekeyEndTime] = `${timestampToTime(val[1])} 23:59:59`;
        }
      } else if (formItemType === 'Date') {
        const { datePickerType, datePickerFormat } = config;
        if (val) {
          // TODO: 改善为根据 datePickerFormat 格式化日期
          if (datePickerType === 'month') {
            params[prop] = timestampToMonth(val);
          } else {
            params[prop] = timestampToTime(val);
          }
        }
      } else if (formItemType === 'DateRangeSeparate') {
        const { valuekeyBeginTime = 'beginTime', valuekeyEndTime = 'endTime', datePickerType } = config;
        if (val && val.valueBegin) {
          params[valuekeyBeginTime] = val.valueBegin;
          if (!datePickerType || datePickerType === 'date') {
            params[valuekeyBeginTime] += ' 00:00:00';
          }
        }
        if (val && val.valueEnd) {
          params[valuekeyEndTime] = val.valueEnd;
          if (!datePickerType || datePickerType === 'date') {
            params[valuekeyEndTime] += ' 23:59:59';
          }
        }
      } else if (formItemType === 'DateTimeRange') {
        const { valuekeyBeginTime = 'beginTime', valuekeyEndTime = 'endTime' } = config;
        if (val && val[0]) {
          params[valuekeyBeginTime] = timestampToTime(val[0], true);
        }
        if (val && val[1]) {
          params[valuekeyEndTime] = timestampToTime(val[1], true);
        }
      } else if (formItemType === 'TimeRange') {
        const { valuekeyBeginTime = 'beginTime', valuekeyEndTime = 'endTime' } = config;
        if (val && val[0]) {
          params[valuekeyBeginTime] = val[0];
        }
        if (val && val[1]) {
          params[valuekeyEndTime] = val[1];
        }
      } else {
        params[prop] = val;
      }
    } catch (error) {
      console.log('[ERR getSearchFormData]', error);
    }
  });

  return params;
}
