/**
  data() {
    return {
      isShowOption: false,
    };
  },
 */
export default {
  mounted() {
    document.addEventListener('click', this.clickDocument);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.clickDocument);
  },
  methods: {
    clickDocument(e) {
      if (this.$refs.selectTrigger && this.$refs.selectTrigger.contains(e.target)) {
        // 单击的是展开options的icon、input
      } else if (this.$refs.selectOptions && this.$refs.selectOptions.contains(e.target)) {
        // 单击的是options
      } else {
        this.isShowOption = false;
      }
    },
    onToggleSelect() {
      this.isShowOption = !this.isShowOption;
    },
    onOpenOption() {
      this.isShowOption = true;
    },
    onCloseOption() {
      this.isShowOption = false;
    },
    clickOption(item) {
      // 单击 option 的处理
    },
  },
};
