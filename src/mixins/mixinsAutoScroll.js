/*
  使用示例
    mixins: [mixinsAutoScroll],
    data() {
      return {
        intervalScroll: null,
        scrollTop: -1,
        scrollElementId: 'battery-warning-info-scroll',
      };
    },
*/
export default {
  mounted() {
    this.startAutoScroll();
  },
  beforeUnmount() {
    this.stopAutoScroll();
  },
  methods: {
    startAutoScroll() {
      this.intervalScroll = setTimeout(() => {
        this.autoScroll();
      }, 100);
    },
    stopAutoScroll() {
      if (this.intervalScroll) {
        clearTimeout(this.intervalScroll);
        this.intervalScroll = null;
      }
    },
    autoScroll() {
      const scrollList = document.getElementById(this.scrollElementId);
      if (!scrollList) {
        return;
      }

      if (this.scrollTop === scrollList.scrollTop) {
        this.scrollTop = -1;
        scrollList.scrollTop = 0;
        setTimeout(() => {
          this.startAutoScroll();
        }, 100);
      } else {
        this.scrollTop = scrollList.scrollTop;
        scrollList.scrollTop = this.scrollTop + 2;
        this.startAutoScroll();
      }
    },
    mouseOverScroll() {
      this.stopAutoScroll();
    },
    mouseLeaveScroll() {
      this.startAutoScroll();
    },
  },
};
