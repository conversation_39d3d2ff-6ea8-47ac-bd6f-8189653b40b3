/*
  使用示例
    mixins: [mixinsPageForm],
    data() {
      return {
        // 通用-Modal
        isShowDetailModal: false,
        detailModalConfig: {},
        // 通用-表单
        isLoadingGetDetail: true,
        detailData: {},
        detailFormConfig: {},
        isLoadingCommitDetail: false,
        // 页面配置-API
        pageConfigAPI: {
          editDetailData: {
            apiFun: editPoolInfo,
            successMsg: '流量池名称修改成功',
          },
        },
        // 页面配置-表单
        pageItemsConfig: {
          流量池名称: {
            editFormItem: {
              valuekey: 'packageName',
              formRules: [{ ruleType: 'length', max: LENGTH_MAX_INPUT }],
            },
          },
        },
      };
    },

    editFormItem 的示例见 createFormItemConfig.js
    表单校验规则的示例见 createFormItemRules.js
    表单校验的一些共通的值写在 define.js 里
*/

import { createFormRules } from './createFormItemRules.js';
import { createFormItemConfig, createFormInitData, formatFormRequestData, formatFormResponseData, createDetailFormConfig } from './createFormItemConfig.js';

export default {
  computed: {
    detailFormRules: function () {
      if (this.detailFormConfig.isReadonly) {
        return {};
      }

      return createFormRules({
        pageItemsConfig: this.pageItemsConfig,
      });
    },
    formItemsConfig: function () {
      return createFormItemConfig({
        pageItemsConfig: this.pageItemsConfig,
        isReadonly: this.detailFormConfig.isReadonly,
      });
    },
  },
  methods: {
    /**
     * 打开 Modal
     */
    async showDetailModal(args = {}) {
      this.isLoadingCommitDetail = false;
      this.isLoadingGetDetail = true;
      this.isShowDetailModal = true;

      const { record = {}, isReadonly = false, modalConfig = {} } = args;

      this.detailModalConfig = modalConfig;
      this.detailFormConfig = createDetailFormConfig({
        isReadonly,
        detailId: record.id,
        detailConfigAPI: this.pageConfigAPI,
      });

      if (record.id) {
        const { isErr = false, detailData = {} } = await this.getDetailData(record.id, record);

        if (isErr) {
          this.closeDetailModalDirectly();
          return;
        }

        const initData = createFormInitData({ formItemsConfig: this.formItemsConfig, detailData, record, detailFormConfig: this.detailFormConfig });

        this.detailData = {
          ...initData,
          ...detailData,
        };
      } else {
        const initData = createFormInitData({ formItemsConfig: this.formItemsConfig, record, detailFormConfig: this.detailFormConfig });
        this.detailData = initData;
      }

      await this.setModalData({ modalArgs: args, record });

      this.isLoadingGetDetail = false;
    },
    getDetailData(id, record) {
      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {},
        replactRequestData,
        convertResponseData = () => {},
      } = this.pageConfigAPI.getDetailData || {};
      return new Promise(async (resolve, reject) => {
        if (!apiFun) {
          resolve({ detailData: record });
        }
        try {
          let requestData = { id };

          convertRequestData({ requestData });
          if (replactRequestData) {
            requestData = replactRequestData({ requestData });
          }

          const response = await apiFun(requestData);

          const responseData = response || {};
          formatFormResponseData({
            formItemsConfig: this.formItemsConfig,
            responseData,
          });
          convertResponseData({ responseData, response });
          resolve({ detailData: responseData });
        } catch (error) {
          resolve({
            isErr: true,
          });
        }
      });
    },
    setModalData() {
      return new Promise((resolve) => {
        resolve();
      });
    },
    /**
     * 关闭 Modal
     */
    closeDetailModal(args = {}) {
      if (args.skipConfirmLeave) {
        this.isShowDetailModal = false;
        this.$emit('close-modal');
      } else {
        this.$confirmCancel({
          title: '提示',
          content: '是否确定放弃当前录入的信息？',
          onOk: () => {
            this.isShowDetailModal = false;
            this.$emit('close-modal');
          },
        });
      }
    },
    closeDetailModalDirectly(args = {}) {
      this.closeDetailModal({
        skipConfirmLeave: true,
      });
    },
    beforeCloseModal() {
      return new Promise((resolve) => {
        if (this.detailFormConfig.isReadonly) {
          resolve();
        } else {
          this.$confirmCancel({
            title: '提示',
            content: '是否确定放弃当前录入的信息？',
            onOk: () => {
              resolve();
            },
          });
        }
      });
    },
    /**
     * 提交
     */
    commitDetailData() {
      this.$refs.detailForm.validate((valid) => {
        if (valid) {
          let configApi = this.pageConfigAPI.addDetailData;
          if (this.detailData.id) {
            configApi = this.pageConfigAPI.editDetailData;
          }

          const { confirm = {} } = configApi;
          const { title = '提示', content = '是否确定保存当前录入的信息？', comments } = confirm;

          this.$confirmSave({
            title,
            content,
            comments,
            onOk: () => {
              this.commitDetailDataAPI();
            },
          });
        } else {
          // this.$Message.error('Fail!');
        }
      });
    },
    commitDetailDataAPI() {
      this.isLoadingCommitDetail = true;

      let requestData = this.getDetailRequestData();

      let configApi = this.pageConfigAPI.addDetailData;
      if (this.detailData.id) {
        configApi = this.pageConfigAPI.editDetailData;
        requestData.id = this.detailData.id;
      }
      const { apiFun, successMsg, errMsg, convertRequestData = () => {}, replactRequestData, convertResponseData = () => {} } = configApi;

      formatFormRequestData({
        formItemsConfig: this.formItemsConfig,
        requestData,
        $util: this.$util,
      });

      convertRequestData({ requestData });
      if (replactRequestData) {
        requestData = replactRequestData({ requestData });
      }

      apiFun(requestData)
        .then((response) => {
          const responseData = response || {};
          this.afterCommitDetailDataSuccess({ responseData, requestData });

          if (successMsg) {
            this.$Message.success(successMsg);
          }
        })
        .catch((error) => {
          // console.log(error);
          this.isLoadingCommitDetail = false;
        });
    },
    getDetailRequestData() {
      let params = {};

      Object.keys(this.formItemsConfig).forEach((key) => {
        const { prop } = this.formItemsConfig[key];
        params[prop] = this.detailData[prop];
      });

      return params;
    },
    afterCommitDetailDataSuccess() {
      this.$emit('submit-success');
      this.closeDetailModalDirectly();
    },
  },
  emits: ['close-modal', 'submit-success'],
};
