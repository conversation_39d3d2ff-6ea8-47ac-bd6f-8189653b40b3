import { createUUID } from '@/libs/lan.js';

export default {
  props: {
    chartWidth: {
      type: Number,
      default: 600,
    },
    chartHeight: {
      type: Number,
      default: 400,
    },
    chartOption: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  computed: {
    chartStyle: function () {
      return {
        width: `${this.chartWidth}px`,
        height: `${this.chartHeight}px`,
      };
    },
  },
  watch: {
    chartOption: function () {
      if (this.myChart) {
        this.updateEchart();
      } else {
        this.buildEchart();
      }
    },
    chartWidth: function (v) {
      setTimeout(() => {
        if (this.myChart) {
          this.myChart.resize();
        }
      }, 10);
    },
    chartHeight: function (v) {
      setTimeout(() => {
        if (this.myChart) {
          this.myChart.resize();
        }
      }, 10);
    },
  },
  methods: {
    buildEchart() {
      // 初始化 echarts 实例
      const myChart = this.$echarts.init(document.getElementById(this.chartId));
      this.myChart = myChart;

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(this.chartOption);
    },
    updateEchart() {
      this.myChart.setOption(this.chartOption);
    },
    createChartId(args = {}) {
      const uuid = createUUID();
      return `echarts_${uuid}`;
    },
  },
  mounted() {
    this.buildEchart();
  },
};
