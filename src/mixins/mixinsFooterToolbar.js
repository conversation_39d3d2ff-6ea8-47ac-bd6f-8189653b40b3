import _ from 'lodash';

export default {
  computed: {
    pageMinwidth: function () {
      return 900;
    },
    pageScrolStyle: function () {
      return `min-width: ${this.pageMinwidth}px; overflow: auto;`;
    },
    footerToolbarStyle: function () {
      if (this.isPageScrollX) {
        return 'bottom: 10px';
      } else {
        return '';
      }
    },
  },
  created() {
    window.addEventListener('resize', this.debounceResizePage);
  },
  mounted() {
    this.debounceResizePage();
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.debounceResizePage);
  },
  methods: {
    debounceResizePage: _.debounce(function () {
      this.resizePage();
    }, 300),
    resizePage() {
      const { mainContentClientWidth } = this.getPageSize();
      if (mainContentClientWidth < this.pageMinwidth) {
        this.isPageScrollX = true;
      }
    },
    getPageSize() {
      let mainContentClientWidth = 0;

      const mainContent = document.getElementsByClassName('i-layout-content-main');
      if (mainContent && mainContent[0]) {
        mainContentClientWidth = mainContent[0].clientWidth;
      }

      return {
        mainContentClientWidth,
      };
    },
  },
};
