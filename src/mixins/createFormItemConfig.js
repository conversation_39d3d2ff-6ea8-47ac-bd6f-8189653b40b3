/* 示例
    [FormItem]
        <FormItem :label="formItemsConfig.套餐名称.label" :prop="formItemsConfig.套餐名称.prop">
          <Input
            v-model.trim="detailData[formItemsConfig.套餐名称.prop]"
            :disabled="detailFormConfig.isReadonly"
            :placeholder="formItemsConfig.套餐名称.placeholder"
            :maxlength="formItemsConfig.套餐名称.maxlength"
          />
        </FormItem>
    [pageItemsConfig]
        套餐名称: {
          editFormItem: {
            // label: '名称',
            valuekey: 'packageName',
            formRules: [{ ruleType: 'required' }, { ruleType: 'length', max: LENGTH_MAX_INPUT }],
          },
        },

    [FormItem]
        <FormItem :label="formItemsConfig.备注.label" :prop="formItemsConfig.备注.prop">
          <Input
            v-model.trim="detailData[formItemsConfig.备注.prop]"
            :disabled="detailModalConfig.isReadonly"
            :placeholder="formItemsConfig.备注.placeholder"
            type="textarea"
            :rows="4"
            :maxlength="formItemsConfig.备注.maxlength"
            show-word-limit
          />
        </FormItem>
    [pageItemsConfig]
        备注: {
          editFormItem: {
            valuekey: 'remarks',
            formRules: [{ ruleType: 'length', max: 200 }],
          },
        },

    [FormItem]
        <FormItem :label="formItemsConfig.套餐类型.label" :prop="formItemsConfig.套餐类型.prop">
          <SelectDic
            v-model:modelValue="detailData[formItemsConfig.套餐类型.prop]"
            :disabled="detailFormConfig.isReadonly"
            :placeholder="formItemsConfig.套餐类型.placeholder"
            :dicType="formItemsConfig.套餐类型.dicType"
          />
        </FormItem>

        import SelectDic from '@/components/select/select-dic.vue';
        components: {
          SelectDic,
        },
    [pageItemsConfig]
        套餐类型: {
          editFormItem: {
            valuekey: 'belongOrg',
            formRules: [{ ruleType: 'required' }],
            dicType: 1301,
          },
        },

    [FormItem]
        <FormItem :label="formItemsConfig.订购互斥套餐.label" :prop="formItemsConfig.订购互斥套餐.prop">
          <Select
            v-model="detailData[formItemsConfig.订购互斥套餐.prop]"
            :disabled="detailFormConfig.isReadonly"
            :placeholder="formItemsConfig.订购互斥套餐.placeholder"
            :multiple="formItemsConfig.订购互斥套餐.multiple"
          >
            <Option v-for="item in packageList" :key="item.value" :value="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
    [pageItemsConfig]
        订购互斥套餐: {
          editFormItem: {
            valuekey: 'exclusionDetailIds',
            multiple: true,
            ruleLabel: '互斥套餐',
            formRules: [{ ruleType: 'num', max: 5 }],
            formItemType: 'SelectMultiple',
          },
        },

    [FormItem]
        <FormItem :label="formItemsConfig.联系电话.label" :prop="formItemsConfig.联系电话.prop">
          <Input
            v-model.trim="detailData[formItemsConfig.联系电话.prop]"
            :disabled="detailModalConfig.isReadonly"
            :placeholder="formItemsConfig.联系电话.placeholder"
          ></Input>
        </FormItem>
    [pageItemsConfig]
        联系电话: {
          editFormItem: {
            valuekey: 'contactTel',
            formRules: [{ ruleType: 'required' }, { ruleType: 'phone' }],
          },
        },

    [FormItem]
        <FormItem
          :label="formItemsConfig.下次巡检时间.label"
          :prop="formItemsConfig.下次巡检时间.prop"
        >
          <DatePicker
            type="datetime"
            v-model="detailData[formItemsConfig.下次巡检时间.prop]"
            :placeholder="formItemsConfig.下次巡检时间.placeholder"
            :disabled="detailModalConfig.isReadonly"
            :editable="false"
          />
        </FormItem>
    [pageItemsConfig]
        下次巡检时间: {
          editFormItem: {
            valuekey: 'nextInspectionTime',
            formRules: [{ ruleType: 'required', validatorType: 'Date' }],
            placeholder: '请选择时间',
          },
        },

    [FormItem]
        <FormItem :label="formItemsConfig.值班日期.label" :prop="formItemsConfig.值班日期.prop">
          <DatePicker
            v-model="detailData[formItemsConfig.值班日期.prop]"
            :disabled="detailModalConfig.isReadonly"
            :placeholder="formItemsConfig.值班日期.placeholder"
            type="daterange"
            :options="formItemsConfig.值班日期.options"
            placement="bottom-end"
            style="width: 260px"
            :editable="false"
          ></DatePicker>
        </FormItem>
    [pageItemsConfig]
        值班日期: {
          editFormItem: {
            valuekey: '_dutyDateRange',
            formRules: [
              { ruleType: 'required', validatorType: 'DateRange' },
              { ruleType: 'date', validatorType: 'DateRange', enableDateType: 'futureAndToday' },
            ],
            placeholder: '请选择日期范围',
            formItemType: 'DateRange',
          },
        },

    [FormItem]
        <FormItem :label="formItemsConfig.班次时间.label" :prop="formItemsConfig.班次时间.prop">
          <TimePicker
            v-model="detailData[formItemsConfig.班次时间.prop]"
            :disabled="detailModalConfig.isReadonly"
            :placeholder="formItemsConfig.班次时间.placeholder"
            type="timerange"
            placement="bottom-end"
            style="width: 168px"
          ></TimePicker>
        </FormItem>
    [pageItemsConfig]
        班次时间: {
          editFormItem: {
            valuekey: '_classesTimeRange',
            formRules: [{ ruleType: 'required', validatorType: 'TimeRange' }],
            placeholder: '请选择时间',
            formItemType: 'TimeRange',
            // valuekeyBeginTime: 'beginTime',
            // valuekeyEndTime: 'endTime',
          },
        },
*/
import { isFunction, cloneDeep } from '@/libs/lan';

export function createFormItemConfig({ pageItemsConfig, isReadonly }) {
  let params = {};

  Object.keys(pageItemsConfig).forEach((key) => {
    const config = pageItemsConfig[key];
    if (config.editFormItem) {
      const { label, valuekey, formRules = [], placeholder, formItemType, ...others } = config.editFormItem;
      const _label = label || key;

      let placeholderPrefix = '';
      switch (formItemType) {
        case 'Select':
        case 'SelectNumber':
        case 'SelectMultiple':
        case 'Cascader':
        case 'Date':
        case 'DateRange':
        case 'DateTimeRange':
          placeholderPrefix = '请选择';
          break;
        default:
          placeholderPrefix = '请输入';
          break;
      }
      if (config.editFormItem.dicType) {
        placeholderPrefix = '请选择';
      }
      // const _placeholder = placeholder || `${placeholderPrefix}${_label}`;
      const _placeholder = placeholder || placeholderPrefix;

      let _minlength, _maxlength, enableDateType, required;
      formRules.forEach((rule) => {
        if (rule.ruleType === 'length') {
          _minlength = rule.min;
          _maxlength = rule.max;
        } else if (rule.ruleType === 'date') {
          enableDateType = rule.enableDateType;
        } else if (rule.ruleType === 'required') {
          required = true;
        }
      });

      let _config = {};
      if (enableDateType) {
        switch (enableDateType) {
          case 'future':
            // 只允许选择将来日期（不可以选择今天）
            _config.options = {
              disabledDate: (date) => {
                return date && date.valueOf() < Date.now();
              },
            };
            break;
          case 'futureAndToday':
            // 只允许选择今天和将来日期
            _config.options = {
              disabledDate: (date) => {
                const oneDay = 86400000;
                if (date) {
                  const time = date.valueOf();
                  const timeNow = Date.now();
                  if (time < timeNow - oneDay) {
                    return true;
                  } else {
                    return false;
                  }
                } else {
                  return false;
                }
              },
            };
            break;
          case 'future6dayAndToday':
            // 只允许选择今天和未来6天（共计7天）
            _config.options = {
              disabledDate: (date) => {
                const oneDay = 86400000;
                if (date) {
                  const time = date.valueOf();
                  const timeNow = Date.now();
                  if (time < timeNow - oneDay) {
                    return true;
                  } else if (time > timeNow + oneDay * 6) {
                    return true;
                  } else {
                    return false;
                  }
                } else {
                  return false;
                }
              },
            };
            break;
          case 'pastAndToday':
            // 只允许选择今天和过去日期
            _config.options = {
              disabledDate: (date) => {
                return date && date.valueOf() > Date.now();
              },
            };
            break;
          default:
            break;
        }
      }

      params[key] = {
        ..._config,
        ...others,
        formItemType,
        label: _label,
        prop: valuekey,
        placeholder: isReadonly ? '' : _placeholder,
        minlength: _minlength,
        maxlength: _maxlength,
        required,
      };
    }
  });

  return params;
}

export function createFormInitData({ formItemsConfig, detailData, record, detailFormConfig }) {
  let initData = {};

  Object.keys(formItemsConfig).forEach((key) => {
    const { formItemType, prop, initValue } = formItemsConfig[key];
    switch (formItemType) {
      case 'InputNumber':
        initData[prop] = null;
        break;
      case 'DateRange':
      case 'TimeRange':
        initData[prop] = [];
        break;
      default:
        break;
    }

    if (initValue !== null && initValue !== undefined) {
      if (isFunction(initValue)) {
        initValue({ initData, prop, detailData, record, detailFormConfig });
      } else {
        initData[prop] = cloneDeep(initValue);
      }
    }
  });

  return initData;
}

export function formatFormRequestData({ formItemsConfig, requestData = {}, $util }) {
  Object.keys(formItemsConfig).forEach((key) => {
    const config = formItemsConfig[key];
    const { formItemType, prop } = config;
    const val = requestData[prop];

    try {
      if (formItemType === 'DateRange') {
        const { valuekeyBeginTime = 'beginTime', valuekeyEndTime = 'endTime' } = config;
        if (val && val[0]) {
          requestData[valuekeyBeginTime] = `${$util.timestampToTime(val[0])} 00:00:00`;
        }
        if (val && val[1]) {
          requestData[valuekeyEndTime] = `${$util.timestampToTime(val[1])} 23:59:59`;
        }
        delete requestData[prop];
      } else if (formItemType === 'TimeRange') {
        const { valuekeyBeginTime = 'beginTime', valuekeyEndTime = 'endTime' } = config;
        requestData[valuekeyBeginTime] = val[0];
        requestData[valuekeyEndTime] = val[1];
        delete requestData[prop];
      }
    } catch (error) {
      console.log('[ERR formatFormRequestData]', error);
    }
  });
}

export function formatFormResponseData({ formItemsConfig, responseData = {} }) {
  Object.keys(formItemsConfig).forEach((key) => {
    const config = formItemsConfig[key];
    const { formItemType, prop } = config;
    const val = responseData[prop];

    switch (formItemType) {
      case 'InputNumber':
      case 'SelectNumber':
        if (val !== null && val !== undefined) {
          responseData[prop] = Number(val);
        }
        break;
      case 'DateRange':
      case 'TimeRange':
        const { valuekeyBeginTime = 'beginTime', valuekeyEndTime = 'endTime' } = config;
        responseData[prop] = [responseData[valuekeyBeginTime], responseData[valuekeyEndTime]];
        break;
      default:
        break;
    }
  });
}

export function createDetailFormConfig({ isReadonly, detailId, detailConfigAPI }) {
  let detailFormConfig = {
    isReadonly,
  };
  if (detailId && !isReadonly) {
    detailFormConfig.isEdit = true;
  }
  if (!detailId && !isReadonly) {
    detailFormConfig.isAdd = true;
  }

  return detailFormConfig;
}
