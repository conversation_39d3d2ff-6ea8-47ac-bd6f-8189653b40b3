/**
  data() {
    return {
      intervalPolling: null,
    };
  },
 */
export default {
  mounted() {
    this.startPolling();
  },
  beforeUnmount() {
    this.clearPolling();
  },
  methods: {
    startPolling() {
      this.intervalPolling = setTimeout(() => {
        this.triggerRefresh();
        this.startPolling();
      }, 1000 * 60 * 60); // 一小时
    },
    clearPolling() {
      if (this.intervalPolling) {
        clearTimeout(this.intervalPolling);
        this.intervalPolling = null;
      }
    },
  },
};
