/*
  使用示例
    mixins: [mixinsPageSetting],
    data() {
      return {
        // 通用
        isLoadingGetDetail: true,
        detailData: {},
        isLoadingCommitDetail: false,
        // 页面配置-API
        pageConfigAPI: {
          getDetailData: {
            apiFun: getDemoDetail,
          },
          editDetailData: {
            apiFun: editDemoItem,
            successMsg: '示例商品修改成功',
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          goBack: {
            routerName: 'demo-list',
          },
        },
        // 其他
        // (页面需要的其他数据，此处省略)
      };
    },
*/

export default {
  computed: {
    pageTitle: function () {
      const { title } = this.getRouteMeta();
      return title;
    },
  },
  methods: {
    /**
     * 返回
     */
    goBack(args = {}) {
      const { goBack = {} } = this.pageConfigButton || {};
      if (goBack.routerName) {
        this.$router.push({
          name: goBack.routerName,
          params: {
            skipConfirmLeave: args.skipConfirmLeave,
          },
        });
      }
    },
    /**
     * 打开页面
     */
    async initDetailPage() {
      this.isLoadingCommitDetail = false;
      this.isLoadingGetDetail = true;

      /*
      const { requiredId = false } = this.getRouteMeta();
      const params = this.getRouteParams();
      const isReadonly = params.isReadonly === 'true' || params.isReadonly === true;
      const record = params.record ? JSON.parse(params.record) : {};
      if (requiredId && !record.id) {
        this.$Message.warning('数据缺失');
        this.goBack({ skipConfirmLeave: true });
        return;
      }
      */
      const params = this.getRouteParams();
      const record = params.record ? JSON.parse(params.record) : {};

      const { isErr = false, detailData = {} } = await this.getDetailData(record);
      if (isErr) {
        this.goBack({ skipConfirmLeave: true });
        return;
      }

      this.detailData = detailData;

      this.isLoadingGetDetail = false;
    },
    getRouteParams() {
      const { params = {} } = this.$route;
      return params;
    },
    getRouteMeta() {
      const { meta = {} } = this.$route;
      return meta;
    },
    getDetailData(record) {
      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {},
        replactRequestData,
        convertResponseData = () => {},
      } = this.pageConfigAPI.getDetailData || {};
      return new Promise(async (resolve, reject) => {
        if (!apiFun) {
          resolve({ detailData: record });
        }

        try {
          let requestData = {};

          convertRequestData({ requestData, record });
          if (replactRequestData) {
            requestData = replactRequestData({ requestData, record });
          }

          const response = await apiFun(requestData);

          const responseData = response || {};
          convertResponseData({ responseData, response });
          resolve({ detailData: responseData });
        } catch (error) {
          resolve({
            isErr: true,
          });
        }
      });
    },
    /**
     * 提交
     */
    commitDetailData() {
      const { confirm = {} } = this.pageConfigAPI.editDetailData || {};
      const { title = '提示', content = '是否确定保存当前录入的信息？', comments } = confirm;

      this.$confirmSave({
        title,
        content,
        comments,
        onOk: () => {
          this.commitDetailDataAPI();
        },
      });
    },
    commitDetailDataAPI() {
      this.isLoadingCommitDetail = true;

      let requestData = this.getDetailRequestData();
      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {},
        replactRequestData,
        convertResponseData = () => {},
      } = this.pageConfigAPI.editDetailData || {};

      convertRequestData({ requestData });
      if (replactRequestData) {
        requestData = replactRequestData({ requestData });
      }

      apiFun(requestData)
        .then((response) => {
          const responseData = response || {};
          this.afterCommitDetailDataSuccess({ responseData });

          if (successMsg) {
            this.$Message.success(successMsg);
          }
        })
        .catch((error) => {
          // console.log(error);
          this.isLoadingCommitDetail = false;
        });
    },
    getDetailRequestData() {
      return {};
    },
    afterCommitDetailDataSuccess() {
      this.goBack({ skipConfirmLeave: true });
    },
  },
};
