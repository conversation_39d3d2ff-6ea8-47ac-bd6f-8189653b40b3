/*
  pageItemsConfig 中的 listColumn 说明：
    valuekey       必须。列的属性值（通常设置成跟后端接口的属性值一样）。
    label          可选。列的 title。省略的时候用 pageItemsConfig 的 key 值。
    renderType     可选。一些常见的 render
    （其他配置项请参考 iView 的 Table 组件）
  pageItemsConfig 中的 listColumn 示例：
    pageItemsConfig: {
      姓名: {
        listColumn: {
          valuekey: 'personName',
        },
      },
      所属组织: {
        listColumn: {
          label: '物资所属组织',
          valuekey: 'belongOrgName',
        },
      },
      创建时间: {
        listColumn: {
          valuekey: 'createTime',
          width: 170,
        },
      },
      创建人: {
        listColumn: {
          valuekey: 'createByName',
          minWidth: 120,
          renderType: 'ellipsis',
        },
      },
      数量: {
        listColumn: {
          valuekey: 'quantity',
          render: (h, params) => {
            return h('div', params.row.quantity + params.row.unit);
          },
        },
      },
      售价: {
        listColumn: {
          label: '售价 (元)',
          valuekey: 'price',
          width: 100,
          renderType: 'number',
          renderPrecision: 2,
        },
      },
    },
*/
import { timestampToTime, fixedNumber } from '@/libs/format.js';

export function createListColumns({ listColumnsOrder, pageItemsConfig }) {
  const _columns = listColumnsOrder.reduce((array, element) => {
    if (pageItemsConfig[element] || element === 'INDEX' || element === 'ACTION' || element === 'CHECKBOX') {
      const config = pageItemsConfig[element] || {};
      const listColumn = config.listColumn || {};
      const { label, valuekey, renderType, renderPrecision, defaultValue = '-', ...others } = listColumn;

      let skipRenderDefault = false;
      if (others.slot) {
        skipRenderDefault = true;
      }

      let item = {};

      switch (element) {
        case 'INDEX':
          skipRenderDefault = true;
          item = {
            type: 'index',
            title: '序号',
            width: 65,
            align: 'center',
          };
          break;
        case 'ACTION':
          skipRenderDefault = true;
          item = {
            title: '操作',
            key: 'handle',
            width: 200,
            align: 'center',
            slot: 'action',
          };
          break;
        case 'CHECKBOX':
          skipRenderDefault = true;
          item = {
            type: 'selection',
            width: 40,
            align: 'center',
          };
          break;
        default:
          item = {
            title: listColumn.label || element,
            key: listColumn.valuekey,
          };
          break;
      }

      switch (renderType) {
        case 'YMD': // 年-月-日
          item.render = (h, params) => {
            let value = params.row[item.key];
            if (value === null || value === undefined || value === '') {
              value = defaultValue;
            } else {
              value = timestampToTime(new Date(value));
            }
            return h('div', value);
          };
          break;
        case 'YMDHMS': // 年-月-日 时:分:秒
          item.render = (h, params) => {
            let value = params.row[item.key];
            if (value === null || value === undefined || value === '') {
              value = defaultValue;
            } else {
              value = timestampToTime(new Date(value), true);
            }
            return h('div', value);
          };
          break;
        case 'ellipsis':
          item.ellipsis = true;
          item.render = (h, params) => {
            let value = params.row[item.key];
            if (value === null || value === undefined || value === '') {
              value = defaultValue;
            }
            return h(
              'div',
              {
                style: {
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                },
                attrs: {
                  title: value,
                },
              },
              value
            );
          };
          break;
        case 'number':
          item.align = 'right';
          item.render = (h, params) => {
            let value = params.row[item.key];
            if (value === null || value === undefined || value === '') {
              value = defaultValue;
            } else {
              value = fixedNumber(value, renderPrecision);
            }
            return h(
              'div',
              {
                style: {
                  marginRight: '4px',
                },
              },
              value
            );
          };
          break;
        default:
          if (!skipRenderDefault) {
            item.render = (h, params) => {
              let value = params.row[item.key];
              if (value === null || value === undefined || value === '') {
                value = defaultValue;
              }
              return h('div', value);
            };
          }
          break;
      }

      array.push({
        ...item,
        ...others,
      });
    }

    return array;
  }, []);

  return _columns;
}
