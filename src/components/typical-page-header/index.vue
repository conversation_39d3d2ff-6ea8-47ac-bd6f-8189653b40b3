<template>
  <div v-if="headerTitle" class="page-header">
    <div class="title">
      {{ headerTitle }}
    </div>
    <div v-if="headerContent" class="content">
      {{ headerContent }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'TypicalPageHeader',

  props: {
    headerTitle: {
      type: String,
      default: '',
    },
    headerContent: {
      type: String,
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
  },

  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.page-header {
  width: 100%;
  // height: 88px;
  padding: 16px 32px;
  background: #ffffff;
  box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.08);
  .title {
    font-size: 20px;
    font-weight: 600;
    color: #17233d;
    line-height: 28px;
  }
  .content {
    font-size: 14px;
    font-weight: 400;
    color: #808695;
    margin-top: 8px;
  }
}
</style>
