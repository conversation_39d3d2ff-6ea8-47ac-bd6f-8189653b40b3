<template>
  <div style="display: flex">
    <Checkbox v-if="hasCheckAll" :indeterminate="checkAll.indeterminate" :model-value="checkAll.value" :disabled="disabled" @click.prevent="handleCheckAll">
      {{ checkAllLabel }}
    </Checkbox>
    <CheckboxGroup v-model="value" @on-change="changedata">
      <Checkbox v-for="(item, index) in optionlist" :key="index" :label="item.value" :disabled="disabled">{{ item.label }}</Checkbox>
    </CheckboxGroup>
  </div>
</template>
<script>
import { getDicData } from '@/api/common';

export default {
  props: {
    modelValue: {
      type: Array,
      default: function () {
        return [];
      },
    },
    dicType: {
      type: [String, Number],
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    hasCheckAll: {
      type: Boolean,
      default: false,
    },
    checkAllDicVal: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      indeterminate: false,
      checkAllLabel: '全选',
      value: [],
      optionlist: [],
    };
  },
  computed: {
    checkAll: function () {
      if (this.optionlist.length === 0) {
        return {
          indeterminate: false,
          value: false,
        };
      } else if (this.value.length === this.optionlist.length) {
        return {
          indeterminate: false,
          value: true,
        };
      } else if (this.value.length > 0) {
        return {
          indeterminate: true,
          value: false,
        };
      } else {
        return {
          indeterminate: false,
          value: false,
        };
      }
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.value = val || [];
      },
    },
  },
  created() {
    if (this.dicType) {
      this.getOptionList();
    }
  },
  methods: {
    handleCheckAll() {
      if (this.checkAll.value) {
        this.value = [];
      } else {
        this.value = [];
        this.optionlist.forEach(({ value, label }) => {
          this.value.push(value);
        });
      }
      this.$emit('update:modelValue', this.value);
      this.$emit('validate-form-field');
    },
    getOptionList() {
      getDicData({ dicType: this.dicType }).then((response) => {
        if (this.hasCheckAll && this.checkAllDicVal) {
          this.optionlist = [];
          response.forEach(({ value, label }) => {
            if (value === this.checkAllDicVal) {
              this.checkAllLabel = label;
            } else {
              this.optionlist.push({
                value,
                label,
              });
            }
          });
        } else {
          this.optionlist = response;
        }
      });
    },
    changedata(val) {
      this.$emit('update:modelValue', val);
    },
  },
  emits: ['update:modelValue', 'validate-form-field'],
};
</script>
<style scoped></style>
