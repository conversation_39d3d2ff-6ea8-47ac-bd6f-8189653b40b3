<template>
  <div class="search">
    <Form ref="searchForm" :label-width="labelWidth" :model="searchFormData" label-colon class="search-form" @submit.prevent>
      <div class="searchitems">
        <Row style="width: 100%">
          <Col v-for="(item, index) in searchItemsRegular" :key="'regular' + index" :span="searchFormItems[item].spanCol || spanCol">
            <SearchFormItem :configFormItem="searchFormItems[item]" :dataForm="searchFormData" />
          </Col>
          <Col v-for="(item, index) in searchItemsExpand" v-show="isExpandSearch" :key="'expand' + index" :span="searchFormItems[item].spanCol || spanCol">
            <SearchFormItem :configFormItem="searchFormItems[item]" :dataForm="searchFormData" />
          </Col>
        </Row>
        <div class="searchbtn">
          <Button @click="handleReset">重置</Button>
          <Button type="primary" style="margin: 0 8px" @click="handleSearch">查询</Button>
          <slot name="custombtn"></slot>
          <div v-if="hasExpandSearch" class="expand-all" @click="handleExpandSearch">
            <span>{{ isExpandSearch ? '收起' : '展开' }}</span>
            <Icon :type="isExpandSearch ? 'ios-arrow-up' : 'ios-arrow-down'" size="15" />
          </div>
        </div>
      </div>
    </Form>
  </div>
</template>

<script>
import { cloneDeep, take, drop } from '@/libs/lan';
import SearchFormItem from './search-form-item';

export default {
  name: 'SearchFrom',
  components: {
    SearchFormItem,
  },
  props: {
    searchFormInitData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    searchFormListOrder: {
      type: Array,
      default: function () {
        return [];
      },
    },
    searchFormItems: {
      type: Object,
      default: function () {
        return {};
      },
    },
    colPerRow: {
      type: Number,
      default: 4,
    },
    labelWidth: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      isExpandSearch: false,
      searchFormData: {},
    };
  },
  computed: {
    searchItemsRegular: function () {
      return take(this.searchFormListOrder, this.firstLineNum);
    },
    searchItemsExpand: function () {
      return drop(this.searchFormListOrder, this.firstLineNum);
    },
    spanCol: function () {
      return 24 / this.colPerRow;
    },
    firstLineNum: function () {
      let num = 0;
      let totalSpan = 0;
      for (let index = 0; index < this.searchFormListOrder.length; index++) {
        const key = this.searchFormListOrder[index];
        if (this.searchFormItems[key]) {
          const { spanCol = this.spanCol } = this.searchFormItems[key];
          totalSpan += spanCol;
          if (totalSpan > 24) {
            break;
          } else {
            num++;
          }
        }
      }

      return num;
    },
    hasExpandSearch: function () {
      return this.searchFormListOrder.length > this.colPerRow;
    },
  },
  created() {
    this.searchFormData = cloneDeep(this.searchFormInitData);
  },
  methods: {
    /**
     * 展开搜索搜索项
     */
    handleExpandSearch() {
      this.isExpandSearch = !this.isExpandSearch;
    },

    /**
     * 重置搜索
     */
    handleReset() {
      // this.$refs.searchForm.resetFields();
      this.searchFormData = cloneDeep(this.searchFormInitData);
      this.$nextTick(() => {
        this.$emit('reset-search', this.searchFormData);
      });
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.$emit('on-search', this.searchFormData);
    },

    /**
     * 返回搜索值供其他功能使用
     */
    getSearchFormData() {
      return cloneDeep(this.searchFormData);
    },

    /**
     * 变更搜索项的值
     */
    setSearchFormData(searchFormData) {
      Object.keys(searchFormData).forEach((key) => {
        this.searchFormData[key] = searchFormData[key];
      });
    },
    setSearchFormDataAndSearch(searchFormData) {
      this.setSearchFormData(searchFormData);
      this.$emit('on-search', this.searchFormData);
    },
    setSearchFormDataAndReSearch(searchFormData) {
      this.setSearchFormData(searchFormData);
      this.$emit('reset-search', this.searchFormData);
    },
  },
  emits: ['reset-search', 'on-search'],
};
</script>

<style lang="less" scoped>
.search {
  padding: 16px 12px 0;
  background-color: #fff;
  .searchitems {
    display: flex;
    justify-content: space-between;
  }
  &-form {
    .ivu-col {
      padding-right: 12px;
    }
    .searchbtn {
      width: 220px;
      margin-right: 12px;
      justify-content: flex-end;
      display: flex;
    }
    .expand-all {
      color: #2d8cf0;
      margin-top: 5px;
      cursor: pointer;
      > span {
        margin-right: 4px;
      }
    }
  }
}
</style>
