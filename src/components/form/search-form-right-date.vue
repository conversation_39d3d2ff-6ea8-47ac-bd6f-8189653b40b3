<!-- 页面右上角进行日期搜索的搜索框 -->
<template>
  <div class="search-right">
    <Form ref="searchForm" :label-width="labelWidth" :model="searchFormData" label-colon class="search-right-form" @submit.prevent>
      <div class="searchitems">
        <SearchFormItem :configFormItem="searchFormItems[searchFormListOrder[0]]" :dataForm="searchFormData" />
        <div class="searchbtn">
          <Button @click="handleReset">重置</Button>
          <Button type="primary" style="margin: 0 8px" @click="handleSearch">查询</Button>
        </div>
      </div>
    </Form>
  </div>
</template>

<script>
import { cloneDeep } from '@/libs/lan';
import SearchFormItem from './search-form-item';

export default {
  name: 'SearchFrom',
  components: {
    SearchFormItem,
  },
  props: {
    searchFormInitData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    searchFormListOrder: {
      type: Array,
      default: function () {
        return [];
      },
    },
    searchFormItems: {
      type: Object,
      default: function () {
        return {};
      },
    },
    labelWidth: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      searchFormData: {},
    };
  },
  created() {
    this.searchFormData = cloneDeep(this.searchFormInitData);
  },
  methods: {
    /**
     * 重置搜索
     */
    handleReset() {
      // this.$refs.searchForm.resetFields();
      this.searchFormData = cloneDeep(this.searchFormInitData);
      this.$nextTick(() => {
        this.$emit('reset-search', this.searchFormData);
      });
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.$emit('on-search', this.searchFormData);
    },

    /**
     * 返回搜索值供其他功能使用
     */
    getSearchFormData() {
      return cloneDeep(this.searchFormData);
    },

    /**
     * 变更搜索项的值
     */
    setSearchFormData(searchFormData) {
      Object.keys(searchFormData).forEach((key) => {
        this.searchFormData[key] = searchFormData[key];
      });
    },
    setSearchFormDataAndSearch(searchFormData) {
      this.setSearchFormData(searchFormData);
      this.$emit('on-search', this.searchFormData);
    },
    setSearchFormDataAndReSearch(searchFormData) {
      this.setSearchFormData(searchFormData);
      this.$emit('reset-search', this.searchFormData);
    },
  },
  emits: ['reset-search', 'on-search'],
};
</script>

<style lang="less" scoped>
.search-right {
  padding: 14px 0px 0px 12px;
  height: 50px;
  background-color: #fff;
  .searchitems {
    display: flex;
    justify-content: space-between;
  }
  &-form {
    .searchbtn {
      width: 150px;
      margin-right: 12px;
      justify-content: flex-end;
      display: flex;
    }
  }
}
</style>
