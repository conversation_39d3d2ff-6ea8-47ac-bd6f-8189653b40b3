<template>
  <div class="biyi-spin" :style="{ 'background-color': bgColor ? 'rgba(255, 255, 255, 0.85)' : '' }">
    <img class="biyi-spin-loading biyi-spin-loading-animation" src="../../assets/images/logo-small.png" alt="" />
    <p>{{ text }}</p>
  </div>
</template>

<script>
export default {
  name: 'BiyiSpin',
  props: {
    text: {
      type: String,
      default: '数据加载中',
    },
    bgColor: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="less">
.biyi-spin {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  // background-color: rgba(255, 255, 255, 0.85);

  p {
    margin-top: 8px;
  }

  &-loading {
    width: 36px;
    height: 36px;
    object-fit: contain;
    opacity: 1;
  }

  &-loading-animation {
    animation-name: rotate-animation;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
    animation-duration: 0.85s;
  }
}

@keyframes rotate-animation {
  0% {
    transform: rotateY(0);
  }
  100% {
    transform: rotateY(360deg);
  }
}
</style>
