// 数据来源：http://datav.aliyun.com/portal/school/atlas/area_selector
import alladcode from './all.json';
import geoJson100000 from './100000_full.json';

const regionConfig = {
  // 全国
  100000: {
    geoJson: geoJson100000,
  },
};

// json目录下放的是不含子区域的数据；json-full目录下放的是含子区域的数据（可用于继续下钻）
/*
const files = import.meta.globEager('./json/*.json');
for (const path in files) {
  regionConfig[path.replace(/(\.\/|\.json)/g, '')] = {
    geoJson: files[path].default,
  };
}
*/
const files = import.meta.globEager('./json-full/*.json');
for (const path in files) {
  regionConfig[path.replace(/(\.\/json-full\/|\_full|\.json)/g, '')] = {
    geoJson: files[path].default,
  };
}

export default regionConfig;

let alladcodeDict = {};
let alladnameDict = {};
alladcode.forEach(function (item) {
  alladcodeDict[item.adcode] = item;
  alladnameDict[item.name] = item;
});

let alladcentroidDict = {};
geoJson100000.features.forEach(function (item) {
  if (item.properties) {
    // 130000 和 620000 没有 centroid
    alladcentroidDict[item.properties.adcode] = item.properties.centroid || item.properties.center;
  }
});

export function getGeoJson(regionCode) {
  return new Promise((resolve, reject) => {
    const config = regionConfig[regionCode];
    if (config) {
      resolve(config.geoJson);
    } else {
      reject(new Error(`缺少相关数据! (code: ${regionCode})`));
    }
  });
}

export function getRegion(regionName) {
  // const region = alladcode.filter((areaJson) => areaJson.name === regionName)[0];
  const region = alladnameDict[regionName] || {};
  return {
    regionCode: region.adcode,
    regionName: region.name,
  };
}

export function getParentRegion(regionCode) {
  const region = alladcodeDict[regionCode] || {};
  if (region.parent) {
    const parentRegion = alladcodeDict[region.parent] || {};
    return {
      regionCode: parentRegion.adcode,
      regionName: parentRegion.name,
    };
  } else {
    return {};
  }
}

export function getRegionInfo(regionCode) {
  const region = alladcodeDict[regionCode] || {};
  const centroid = alladcentroidDict[regionCode];
  return {
    regionName: region.name,
    regionCentroid: centroid,
  };
}
