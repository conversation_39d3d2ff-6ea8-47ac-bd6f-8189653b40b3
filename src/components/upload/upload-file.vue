<template>
  <PhotoWall v-if="uploadFileList.length > 0" :imgList="uploadFileList" :hasRemove="hasRemove" :styleWrap="{ display: 'inline-block' }" :imgSize="imgSize" @remove-img="handleRemove" />
  <div :class="uploadWrapClass">
    <Upload
      :action="uploadAction"
      :headers="uploadAxiosHeaders"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="onRemove"
      :show-upload-list="showUploadDefaultList"
      :default-file-list="uploadFileList"
      :disabled="disabledUpload"
      :accept="acceptFileType"
      :before-upload="beforeUpload"
    >
      <Button v-if="isShowUpload" icon="md-cloud-upload" :loading="isUploading">选择文件</Button>
    </Upload>
    <div v-if="isShowUpload" class="my-upload-suffix">{{ suffixMsg }}</div>
  </div>
</template>
<script>
import PhotoWall from '../img/PhotoWall';
import mixinsUpload from './mixinsUpload';

export default {
  mixins: [mixinsUpload],
  components: {
    PhotoWall,
  },
  props: {
    showUploadDefaultList: {
      type: Boolean,
      default: true,
    },
    uploadFileUrl: {
      type: String,
    },
  },
  data() {
    return {
      isUploading: false,
      uploadFileList: [],
    };
  },
  computed: {
    uploadWrapClass: function () {
      return {
        'hide-upload-btn': !this.isShowUpload,
      };
    },
  },
};
</script>
<style scoped>
.my-upload-suffix {
  color: #dcdee2;
}
.hide-upload-btn :deep(.ivu-upload-list) {
  margin-top: -35px;
}
</style>
