<template>
  <div>
    <PhotoWall :imgList="uploadFileList" :hasRemove="hasRemove" :styleWrap="{ display: 'inline-block' }" :imgSize="imgSize" @remove-img="handleRemove" />
    <template v-if="isShowUpload">
      <Upload
        :action="uploadBase64 ? '' : uploadAction"
        :headers="uploadAxiosHeaders"
        :on-success="handleSuccess"
        :on-error="handleError"
        style="display: inline-block"
        :show-upload-list="false"
        :disabled="disabledUpload"
        :accept="acceptFileType"
        :before-upload="uploadBase64 ? beforeUploadBase64 : beforeUpload"
      >
        <div
          v-if="disabledUpload"
          :style="{
            width: imgSize + 'px',
            height: imgSize + 'px',
            lineHeight: imgSize + 'px',
          }"
          class="my-upload-img-button-disabled"
        >
          <Icon type="md-alert" size="20"></Icon>
        </div>
        <div
          v-else-if="isUploading"
          :style="{
            width: imgSize + 'px',
            height: imgSize + 'px',
            lineHeight: imgSize + 'px',
          }"
          class="my-upload-img-button-loading"
        >
          <Spin fix></Spin>
        </div>
        <div
          v-else
          :style="{
            width: imgSize + 'px',
            height: imgSize + 'px',
            lineHeight: imgSize + 'px',
          }"
          class="my-upload-img-button"
        >
          <Icon type="ios-camera" size="20"></Icon>
        </div>
      </Upload>
    </template>
    <div v-if="isShowUpload" class="my-upload-suffix" :style="{ lineHeight: imgSize + 'px' }">{{ suffixMsg }}</div>
  </div>
</template>
<script>
import PhotoWall from '../img/PhotoWall';
import mixinsUpload from './mixinsUpload';
import { createUUID } from '@/libs/lan';

export default {
  mixins: [mixinsUpload],
  components: {
    PhotoWall,
  },
  props: {
    suffixMsg: {
      type: String,
      default: '支持JPG、JPEG、PNG、BMP格式，图片小于5M',
    },
    uploadFileMaxSize: {
      type: Number,
      default: 5, // 文件小于5M
    },
    uploadFileMaxSizeUnit: {
      type: String,
      default: 'MB',
    },
    uploadAcceptFileType: {
      type: String,
      default: 'image/jpg, image/jpeg, image/png, image/bmp',
    },
    uploadFormatError: {
      type: String,
      default: '只支持上传 JPG、JPEG、PNG、BMP 格式的文件!',
    },
    uploadBase64: {
      // 在上传图片后，将图片转成base64格式的字符串。（跟后端之间的接口数据格式也是base64格式的字符串）
      type: Boolean,
      default: false,
    },
    uploadFileUrl: {
      type: String,
    },
  },
  data() {
    return {
      isUploading: false,
      uploadFileList: [],
    };
  },
  methods: {
    beforeUploadBase64(file) {
      if (this.beforeUpload(file)) {
        this.getBase64(file)
          .then((res) => {
            this.handleSuccessBase64(res);
          })
          .catch((error) => {
            this.handleError();
          });
      }
      return false; // 阻止默认的上传
    },
    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        let imgResult = '';
        reader.readAsDataURL(file);
        reader.onload = function () {
          imgResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(imgResult);
        };
      });
    },
    handleSuccessBase64(base64Str) {
      this.uploadFileList.push({
        filePath: createUUID(),
        fileUrl: base64Str,
      });

      this.$emit('on-change-filelist', {
        fileList: this.uploadFileList,
      });

      this.isUploading = false;
    },
  },
  emits: ['on-change-filelist'],
};
</script>
<style scoped>
.my-upload-img-button {
  cursor: pointer;
  border: 1px dashed #dcdee2;
  border-radius: 4px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s ease;
}
.my-upload-img-button:hover {
  border: 1px dashed #2d8cf0;
}
.my-upload-img-button-disabled {
  cursor: not-allowed;
  border: 1px dashed #dcdee2;
  border-radius: 4px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s ease;
}
.my-upload-img-button-loading {
  position: relative;
  overflow: hidden;
}
.my-upload-suffix {
  display: inline-block;
  margin-left: 10px;
  vertical-align: top;
  color: #dcdee2;
}
</style>
