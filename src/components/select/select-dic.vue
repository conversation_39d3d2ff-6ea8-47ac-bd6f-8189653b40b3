<template>
  <div>
    <Select
      v-model="value"
      :placeholder="placeholder"
      :clearable="clearable"
      :disabled="disabled"
      :multiple="multiple"
      :placement="placement"
      @on-change="changedata"
    >
      <Option v-for="(item, index) in optionlist" :key="index" :value="item.value">
        {{ item.label }}
      </Option>
    </Select>
  </div>
</template>
<script>
import { getDicData } from '@/api/common';
import { isArray } from '@/libs/lan';

export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },

    modelValue: {
      type: [String, Number, Array],
      default: '',
    },
    spliceOption: {
      type: Array,
      default: function () {
        return [];
      },
    },
    dicType: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    placement: {
      type: String,
      default: 'bottom-start',
    },
  },
  emits: ['update:modelValue', 'change-data'],
  data() {
    return {
      value: '',
      optionlist: [],
    };
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (isArray(val)) {
          this.value = val;
        } else {
          this.value = `${val}`;
        }
      },
    },
    list: {
      handler(newVal) {
        this.optionlist = newVal;
      },
      immediate: true,
    },
  },
  created() {
    if (this.dicType) {
      this.getOptionList();
    }
  },
  methods: {
    getOptionList() {
      getDicData({ dicType: this.dicType }).then((response) => {
        if (this.spliceOption) {
          response = response.filter((item) => {
            return !this.spliceOption.includes(item.value);
          });
        }
        this.optionlist = response;
      });
    },
    changedata(val) {
      this.$emit('update:modelValue', val);
      this.$emit('change-data', val);
    },
  },
};
</script>
