<template>
  <div class="zoom-wrap">
    <div class="zoom-box" :style="{ width: zoomBoxWidth + 'px', height: zoomBoxHeight + 'px' }">
      <div class="zoom-content" :style="{ width: zoomContentWidth + 'px', height: zoomContentHeight + 'px' }">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
import mixinsResize from '@/mixins/mixinsResize';
export default {
  mixins: [mixinsResize],
  props: {
    minusWidth: {
      type: Number,
      default: 0,
    },
    minusHeight: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      zoomContent: 1,
    };
  },
  computed: {
    zoomContentWidth() {
      return 1712 - this.minusWidth;
    },
    zoomContentHeight() {
      return this.minusHeight;
    },
    zoomBoxWidth() {
      return this.zoomContentWidth * this.zoomContent;
    },
    zoomBoxHeight() {
      return this.zoomContentHeight * this.zoomContent;
    },
  },
  methods: {
    resizePage() {
      const { pageHeight, pageWidth } = this.getPageSize();
      this.zoomContent = Math.min(pageWidth / this.zoomContentWidth, pageHeight / this.zoomContentHeight);
      const zoomContent = document.getElementsByClassName('zoom-content');
      if (zoomContent && zoomContent[0]) {
        zoomContent[0].style.transform = `scale(${this.zoomContent})`;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.zoom-wrap {
  width: 100%;
  // height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .zoom-content {
    transform-origin: 0 0;
  }
}
</style>
