<template>
  <span>
    <div :style="styleWrap">
      <div
        v-for="item in imgList"
        :key="item.filePath"
        class="my-img-list"
        :style="{ width: imgSize + 'px', height: imgSize + 'px', lineHeight: imgSize + 'px' }"
      >
        <template v-if="item.status === 'finished' || item.status === undefined">
          <ImgUrl :filePath="item.filePath" :imgSrc="item.fileUrl" />
          <div class="my-img-list-cover">
            <Icon type="ios-eye-outline" @click="openImageView(item.filePath, item.fileUrl)"></Icon>
            <Icon v-if="hasRemove" type="ios-trash-outline" @click="handleRemove(item.filePath)"></Icon>
          </div>
        </template>
        <template v-else>
          <Progress v-if="item.showProgress" :percent="item.percentage" hide-info></Progress>
        </template>
      </div>
    </div>
    <Modal v-model="showImageView" title="查看大图" draggable scrollable :width="imgModalWidth">
      <div class="my-img-view" :style="{ width: imgModalWidth - 32 + 'px' }">
        <ImgUrl :filePath="filePathImageView" :imgSrc="srcImageView" />
      </div>
      <template v-slot:footer>
        <div class="my-img-view-close-button">
          <Button type="primary" @click="closeImageView">关闭</Button>
        </div>
      </template>
    </Modal>
  </span>
</template>
<script>
import ImgUrl from './ImgUrl.vue';

export default {
  components: {
    ImgUrl,
  },
  props: {
    imgList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    hasRemove: {
      type: Boolean,
      default: false,
    },
    styleWrap: {
      type: Object,
      default: function () {
        return {};
      },
    },
    imgSize: {
      type: Number,
      default: 58,
    },
    imgModalWidth: {
      type: Number,
      default: 416,
    },
  },
  data() {
    return {
      showImageView: false,
      filePathImageView: undefined,
      srcImageView: undefined,
    };
  },
  methods: {
    openImageView(filePath, fileUrl) {
      this.filePathImageView = filePath;
      this.srcImageView = fileUrl;
      this.showImageView = true;
    },
    closeImageView() {
      this.filePathImageView = undefined;
      this.srcImageView = undefined;
      this.showImageView = false;
    },
    handleRemove(filePath) {
      this.$emit('remove-img', filePath);
    },
  },
  emits: ['remove-img'],
};
</script>
<style scoped>
.my-img-list {
  display: inline-block;
  text-align: center;
  overflow: hidden;
  background: #fff;
  position: relative;
  margin-right: 4px;
}
.my-img-list img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.my-img-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}
.my-img-list:hover .my-img-list-cover {
  display: block;
}
.my-img-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}
.my-img-view {
  text-align: center;
}
.my-img-view img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.my-img-view-close-button button {
  width: 100%;
  height: 40px;
  padding: 0 15px;
  font-size: 16px;
  border-radius: 4px;
}
</style>
