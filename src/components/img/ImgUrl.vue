<template>
  <img v-if="isShowImg" :src="_imgSrc" />
</template>
<script>
import { URL_FILE_GROUP } from '@/define';

export default {
  props: {
    filePath: {
      type: String,
      default: '',
    },
    imgSrc: {
      type: String,
      default: '',
    },
  },
  computed: {
    isShowImg: function () {
      return this.filePath !== '' || this.imgSrc;
    },
    _imgSrc: function () {
      if (this.imgSrc) {
        return this.imgSrc;
      } else {
        return URL_FILE_GROUP + this.filePath;
      }
    },
  },
};
</script>
<style scoped></style>
