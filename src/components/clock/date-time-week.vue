<template>
  <div class="dashboard-time-wrap">
    <div class="dashboard-time" :style="{ color, fontSize }">{{ dateTime }}</div>
  </div>
</template>
<script>
import { formatDateTime } from '@/libs/time';

export default {
  props: {
    color: {
      type: String,
      default: '#d8d8d8',
    },
    fontSize: {
      type: String,
      default: '18px',
    },
  },
  data() {
    return {
      interval: null,
      dateTime: this.formatCurrentTime(),
    };
  },
  beforeMount() {
    this.polling();
  },
  beforeUnmount() {
    this.clearPolling();
  },
  methods: {
    polling() {
      this.interval = setTimeout(() => {
        this.refreshData();
      }, 1000);
    },
    clearPolling() {
      if (this.interval) {
        clearTimeout(this.interval);
        this.interval = null;
      }
    },
    refreshData() {
      this.clearPolling();
      this.dateTime = this.formatCurrentTime();
      this.polling();
    },
    formatCurrentTime() {
      const { year, month, day, h, m, s, week } = formatDateTime();
      if (m === '00' && s === '00') {
        this.$emit('hourly-update');
      }
      return `${year}-${month}-${day} ${h}:${m}:${s} ${week}`;
    },
  },
  emits: ['hourly-update'],
};
</script>
<style lang="less" scoped>
.dashboard-time-wrap {
  position: relative;
  width: 260px;
  height: 50px;
  .dashboard-time {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    font-weight: 500;
  }
}
</style>
