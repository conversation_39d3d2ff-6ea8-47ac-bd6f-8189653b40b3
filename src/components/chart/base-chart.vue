<template>
  <div ref="chartDom" :style="chartStyle"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import 'echarts-liquidfill';
import { ref, computed, watch, onMounted } from 'vue';

const props = defineProps({
  chartWidth: {
    type: Number,
    default: 600,
  },
  chartHeight: {
    type: Number,
    default: 400,
  },
  chartOption: {
    type: Object,
    default: function () {
      return {};
    },
  },
  notMerge: {
    type: Boolean,
    default: false,
  },
});

const chartDom = ref();
let myChart;

const chartStyle = computed(() => {
  return {
    width: `${props.chartWidth}px`,
    height: `${props.chartHeight}px`,
  };
});

watch(
  () => props.chartOption,
  () => {
    if (myChart) {
      updateEchart();
    } else {
      buildEchart();
    }
  }
);

watch(
  () => props.chartWidth,
  () => {
    setTimeout(() => {
      if (myChart) {
        myChart.resize();
      }
    }, 10);
  }
);

watch(
  () => props.chartHeight,
  () => {
    setTimeout(() => {
      if (myChart) {
        myChart.resize();
      }
    }, 10);
  }
);

function buildEchart() {
  // 初始化 echarts 实例
  myChart = echarts.init(chartDom.value);

  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(props.chartOption, props.notMerge);
}

function updateEchart() {
  myChart.setOption(props.chartOption, props.notMerge);
}

onMounted(() => {
  buildEchart();
});
</script>

<style lang="less" scoped></style>
