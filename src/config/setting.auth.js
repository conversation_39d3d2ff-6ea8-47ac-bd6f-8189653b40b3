const SettingAuth = {
  // 权限组件内部，接口请求返回错误时，弹窗的类型，可选值为 Message 或 Notice
  errorModalType: 'Message',
  // 是否开启cas登录
  hasCasLogin: false,
  // 是否开启邮箱验证
  validateEmailLogin: false,
  // cas登录地址
  casLoginUrl: '',
  // cas登出
  casLogoutUrl: '',
  // cookie中token对应的key名称。默认是
  tokenName: 'Authorization',
  // 密码加密方式的publicKey，默认是
  rsaPublicKey: window.$publicKey,
  // 权限后端接口地址
  authAPIUrl: window.$biyiAuthAPIUrl || 'http://localhost:9004',
};
export default SettingAuth;
