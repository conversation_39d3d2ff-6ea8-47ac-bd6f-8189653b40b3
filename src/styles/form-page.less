.form {
  background: #f4f7f9;
  padding: 16px 24px;
  margin-bottom: 40px;
  height: 100%;
  overflow: auto;
  .ivu-col {
    padding-right: 12px;
  }
  .ivu-card {
    margin-bottom: 16px;
  }
  &-ml {
    margin-left: 8px;
  }
  &-submit {
    background-color: #fff;
    padding: 4px 0;
  }
  .table-btn {
    width: 100%;
    margin-top: 16px;
    margin-bottom: 16px;
    .ivu-btn {
      height: 40px;
    }
  }
  .bottom-btns {
    padding: 16px;
    background-color: #fff;
    display: flex;
    flex-direction: row-reverse;
    .bottom-btn {
      margin-left: 16px;
    }
  }
  .form-content {
    padding: 16px;
    background-color: #fff;
    height: calc(~'100vh - 260px');
  }
}
:deep(.ivu-table-header thead tr th) {
  padding: 12px 0;
}
:deep(.ivu-table-fixed-header thead tr th) {
  padding: 12px 0;
}
:deep(.ivu-table-cell-with-selection) {
  text-overflow: unset;
}
