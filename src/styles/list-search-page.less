.page-wrap {
  height: 100%;
  padding: 0px 24px 24px;
}
.base-table {
  height: 100%;
  padding: 16px;
  position: relative;
  overflow: auto;
  border-radius: 4px;
  background-color: #fff;
  .search {
    padding: 16px 12px 0;
    background-color: #fff;
    .searchitems {
      display: flex;
      justify-content: space-between;
    }
    &-form {
      .ivu-col {
        padding-right: 12px;
      }
      .searchbtn {
        width: 220px;
        margin-right: 12px;
        justify-content: flex-end;
        display: flex;
      }
      .expand-all {
        color: #2d8cf0;
        margin-top: 5px;
        cursor: pointer;
        > span {
          margin-right: 4px;
        }
      }
    }
  }
  .content {
    padding: 0px 16px 16px;
    &-btn {
      display: flex;
      margin-bottom: 16px;
      > .ivu-btn:not(:last-child) {
        margin-right: 8px;
      }
    }

    &-msg {
      font-weight: 700;
      margin-bottom: 12px;
    }

    .action-btn {
      color: #2d8cf0;
      cursor: pointer;
      position: relative;
      padding: 0 8px;
    }

    /*
    .action-btn:not(:last-child) {
      &:after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate3d(0, -50%, 0) scale(1);
        width: 1px;
        height: 1em;
        background-color: #2d8cf0;
      }
    }
    */

    .action-data {
      color: #2d8cf0;
      cursor: pointer;
      position: relative;
    }
  }
  .page {
    padding: 4px 16px;
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.bottom-btns-center {
  display: flex;
  justify-content: center;
  align-items: center;
  .bottom-btn:not(:first-child) {
    margin-left: 16px;
  }
}
// .ivu-select-dropdown ul {
//   text-align: center;
// }
:deep(.ivu-table-header thead tr th) {
  padding: 12px 0;
}
:deep(.ivu-table-fixed-header thead tr th) {
  padding: 12px 0;
}
:deep(.ivu-table-cell-with-selection) {
  text-overflow: unset;
}
