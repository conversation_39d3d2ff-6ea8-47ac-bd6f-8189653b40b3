#app,
body,
html {
  height: calc(~'100vh - 108px');
}
body {
  background-color: @background-color-base;
  font-size: 14px;
}

// 隐藏滚动条样式
.i-scrollbar-hide {
  &::-webkit-scrollbar {
    width: 0;
  }
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: #e8eaec;
  }
}

// 极简滚动条样式
.i-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: #808695;
    border-radius: 4px;
  }
}

// 去除 Table 的左右边框，更精简
.i-table-no-border {
  .ivu-table th {
    background-color: #fff;
  }
  .ivu-table-wrapper,
  .ivu-table tr:last-child td {
    border: none;
  }
  .ivu-table:before,
  .ivu-table:after {
    display: none;
  }
}

// 自动旋转图片
.auto-rotate-img {
  animation: 3s auto-rotate-img-animation linear infinite normal;
  -webkit-animation: 3s auto-rotate-img-animation linear infinite normal;
}
@keyframes auto-rotate-img-animation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@-webkit-keyframes auto-rotate-img-animation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.auto-rotate-img-slow {
  animation: 10s auto-rotate-img-animation linear infinite normal;
  -webkit-animation: 10s auto-rotate-img-animation linear infinite normal;
}

// 红色点闪烁
@keyframes warning-shine {
  0% {
    transform: scale(0);
    background: #ff0000c2;
  }

  100% {
    transform: scale(0.9);
    background: #ff00000e;
  }
}
