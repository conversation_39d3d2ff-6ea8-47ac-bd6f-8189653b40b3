.hpc-page {
  height: 100%;
  padding: 0px 24px 24px;
  .hpc-page-content {
    height: 100%;
    padding: 10px 16px 16px;
    position: relative;
    overflow: auto;
    border-radius: 4px;
    background-color: #fff;
    .search-form {
      padding-top: 10px;
      padding-left: 10px;
      :deep(.ivu-form .ivu-form-item-label) {
        font-size: 13px;
        color: black;
      }
      :deep(.ivu-select-placeholder) {
        color: gray !important;
      }
      ::-webkit-input-placeholder {
        color: gray !important;
      }
    }
    .page-list {
      border-top: 1px solid #d8d0d0;
      padding-top: 5px;
      margin-top: -10px;
      :deep(.ivu-table-header thead tr th) {
        font-size: 13px;
        background-color: #f5f5f5;
        color: #666;
        border: 1px solid silver;
        border-bottom: 0px;
        padding-top: 2px;
        padding-bottom: 7px;
        .ivu-table-cell {
          height: 15px;
          padding-left: 10px;
        }
      }
      :deep(.ivu-table td) {
        color: black;
        font-size: 13px;
        padding-top: 2px;
        padding-bottom: 7px;
        height: 15px;
        .ivu-table-cell {
          height: 15px;
          padding-left: 10px;
        }
      }
      .page {
        padding: 4px 16px;
      }
    }
  }
}
