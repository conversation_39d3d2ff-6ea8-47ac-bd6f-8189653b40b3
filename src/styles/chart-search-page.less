.page-wrap {
  height: 100%;
  padding: 0px 24px 24px;
}

.base-search-chart {
  height: 100%;
  padding: 16px;
  position: relative;
  overflow: auto;
  border-radius: 4px;
  background-color: #fff;
}

.chart-wrap {
  width: 100%;
  display: flex;
  justify-content: center;

  .chart-box {
    position: relative;
    border: 1px solid rgb(207, 204, 204);
    border-radius: 10px;
  }
}

.chart-no-data {
  margin: 15px;
  border: 1px solid rgb(207, 204, 204);
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: none;
  justify-content: center;
  align-items: center;
  display: flex;
  flex-direction: column;

  .loading-dialog {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4a80e7;
    border-radius: 50%;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .loading-text {
    margin-top: 5px;
    font-size: 14px;
    color: #333;
  }
}


