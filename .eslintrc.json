{
  "globals": {
    "$": true, //jQuery
    "_": true, //Lodash
    "BMap": true, //BMap
    "BMapGL": true //BMapGL
  },
  "env": {
    "browser": true,
    "es2021": true
  },
  "extends": ["plugin:vue/essential", "airbnb-base"],
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": ["vue"],
  "rules": {
    "import/no-unresolved": 0,
    "import/extensions": 0,
    "import/no-extraneous-dependencies": 0,
    "no-param-reassign": 0,
    "vue/no-multiple-template-root": 0,
    // ====== 开发中暂时不校验 ======
    // "no-console": 1, //不建议直接使用console(推荐进行封装)
    "no-console": 0, //不建议直接使用console(推荐进行封装)
    // "no-unused-vars": [
    //   "warn",
    //   { "vars": "all", "args": "all", "ignoreRestSiblings": true }
    // ], //禁止出现未使用过的变量和函数的参数(跟...有关的可以不使用)
    "no-unused-vars": 0,

    // ====== Possible Errors (这些规则与 JavaScript 代码中可能的错误或逻辑错误有关) ======

    //"no-await-in-loop": 2,                    //(非推荐配置)disallow await inside of loops
    //"no-compare-neg-zero" 2,                  //(非推荐配置)disallow comparing against -0
    "no-cond-assign": 2, //禁止条件表达式中出现赋值操作符
    "no-constant-condition": 2, //禁止在条件中使用常量表达式
    "no-control-regex": 2, //禁止在正则表达式中使用控制字符
    "no-debugger": 2, //禁用 debugger
    "no-dupe-args": 2, //禁止 function 定义中出现重名参数
    "no-dupe-keys": 2, //禁止对象属性重复
    "no-duplicate-case": 2, //禁止出现重复的 case 标签
    "no-empty": 2, //禁止出现空语句块
    "no-empty-character-class": 2, //禁止在正则表达式中使用空字符集
    "no-ex-assign": 2, //禁止对 catch 子句的参数重新赋值
    "no-extra-boolean-cast": 2, //禁止不必要的布尔转换(在上下文中比如 if 语句的测试表达式的结果已经被强制转化成了一个布尔值，再通过双重否定（!!）或 Boolean 转化是不必要的)
    //"no-extra-parens": 1,                     //(非推荐配置)不建议存在不必要的圆括号
    "no-extra-semi": 1, //不建议存在不必要的分号
    "no-func-assign": 2, //禁止对 function 声明重新赋值
    "no-inner-declarations": ["warn", "both"], //不建议在{}代码块内部声明变量或函数
    "no-invalid-regexp": 2, //禁止 RegExp 构造函数中存在无效的正则表达式字符串
    "no-irregular-whitespace": ["error", { "skipComments": true }], //禁止不规则的空白(注释除外)
    "no-obj-calls": 2, //禁止将 Math 和 JSON 对象当作函数进行调用
    //"no-prototype-builtins": 2,               //(非推荐配置)禁止直接调用 Object.prototypes 的内置属性
    "no-regex-spaces": 2, //禁止正则表达式字面量中出现多个空格
    "no-sparse-arrays": 2, //禁用稀疏数组(数组中多出逗号)
    //"no-template-curly-in-string": 2,         //(非推荐配置)禁止${something}出现在""或''中(为了ES6的``)
    "no-unexpected-multiline": 2, //禁止使用令人困惑的多行表达式(行尾缺少分号可能导致一些意外情况)
    "no-unreachable": 2, //禁止在 return、throw、continue 和 break 语句后出现不可达代码
    "no-unsafe-finally": 2, //禁止在 finally 语句块中出现控制流语句
    "no-unsafe-negation": 2, //disallow negating the left operand of relational operators
    "use-isnan": 2, //要求调用 isNaN()检查 NaN
    //"valid-jsdoc": ["error", {"requireParamDescription": true, "requireReturnDescription": true}], //(非推荐配置)强制使用有效的 JSDoc 注释
    "valid-typeof": 2, //强制 typeof 表达式与有效的字符串进行比较

    // ====== Best Practices (这些规则是关于最佳实践的，帮助你避免一些问题) ======

    //"accessor-pairs": 2,                      //(非推荐配置)强制 getter 和 setter 在对象中成对出现
    //"array-callback-return": 2,               //(非推荐配置)强制数组方法的回调函数中有 return 语句
    "block-scoped-var": 2, //(非推荐配置)强制把变量的使用限制在其定义的作用域范围内
    //"class-methods-use-this": 2,              //(非推荐配置)Enforce that class methods utilize this
    "complexity": ["warn", 100], //(非推荐配置)圈复杂度大于n(默认是20) 警告
    //"consistent-return": 2,                   //(非推荐配置)要求使用一致的 return 语句(即函数返回值不能是不同类型)
    "curly": 0, //(非推荐配置)if、else、while、for代码块用{}包围
    "default-case": 1, //(非推荐配置)要求 switch 语句中有 default 分支
    "dot-location": ["warn", "property"], //(非推荐配置)要求点操作符和属性放在同一行(换行调用对象方法,点操作符应写在行首)
    "dot-notation": 1, //(非推荐配置)强制尽可能地使用点号来调用对象的属性
    "eqeqeq": ["warn", "smart"], //(非推荐配置)要求使用 === 和 !==
    "guard-for-in": 0, //(非推荐配置)要求 for-in 循环中有一个 if 语句
    //"no-alert": 1,                            //(非推荐配置)禁用 alert、confirm 和 prompt
    "no-caller": 1, //(非推荐配置)禁用 arguments.caller 或 arguments.callee
    "no-case-declarations": 0, //(当前配置非eslint推荐配置)不允许在 case 子句中使用词法声明 (let、const、function 和 class)
    //"no-div-regex": 2,                        //(非推荐配置)禁止除法操作符显式的出现在正则表达式开始的位置
    //"no-else-return": 1,                      //(非推荐配置)禁止在 else 前有 return
    //"no-empty-function": 1,                   //(非推荐配置)禁止出现空函数
    "no-empty-pattern": 2, //禁止使用空解构模式
    //"no-eq-null": 1,                          //(非推荐配置)禁止在没有类型检查操作符的情况下与 null 进行比较
    "no-eval": 2, //(非推荐配置)禁用 eval()
    "no-extend-native": 2, //(非推荐配置)禁止扩展原生对象
    //"no-extra-bind": 1,                       //(非推荐配置)禁止不必要的 .bind() 调用
    //"no-extra-label": 1,                      //(非推荐配置)禁用不必要的标签
    "no-fallthrough": 1, //禁止 case 语句落空

    // vvv 可用的配置太多,先将推荐配置过一遍 vvv

    "no-floating-decimal": 2, //(非推荐配置)浮点型需要写全 禁止.1 或 2.写法
    "no-global-assign": 2, //禁止覆盖原生对象(disallow assignments to native objects or read-only global variables)
    "no-labels": 1, //(非推荐配置)禁止使用label:
    "no-lone-blocks": 2, //(非推荐配置)禁用不必要的嵌套块{}
    "no-loop-func": 2, //(非推荐配置)禁止在循环体中定义函数
    "no-multi-spaces": 1, //(非推荐配置)禁止使用多个空格
    "no-new-func": 2, //(非推荐配置)禁止new Function(...) 写法
    "no-octal": 2, //禁用八进制字面量
    "no-redeclare": 2, //禁止在同一作用域中多次声明同一变量
    "no-return-assign": [2, "always"], //(非推荐配置)不允return时有赋值操作
    "no-self-assign": 2, //禁止自我赋值
    "no-self-compare": 2, //(非推荐配置)禁止自身比较
    "no-sequences": 2, //(非推荐配置)禁止可能导致结果不明确的逗号操作符
    //"no-throw-literal": 2,                    //(非推荐配置)禁止抛出一个直接量,应是Error对象
    "no-unused-expressions": [2, { "allowShortCircuit": true, "allowTernary": true }], //(非推荐配置)禁止出现未使用过的表达式
    "no-unused-labels": 2, //禁用出现未使用过的标签
    "no-useless-call": 2, //(非推荐配置)禁止不必要的 .call() 和 .apply()
    "no-useless-concat": 2, //(非推荐配置)禁止不必要的字符串字面量或模板字面量的连接
    "no-void": 2, //(非推荐配置)禁用void
    //"no-warning-comments": [2, { "terms": ["todo", "fixme", "any other term"], "location": "anywhere" }],//(非推荐配置)禁止在注释中使用特定的警告术语
    "no-with": 2, //(非推荐配置)禁用with

    // ====== Variables (这些规则与变量声明有关) ======

    "no-delete-var": 2, //禁止删除变量
    "no-shadow-restricted-names": 2, //(非推荐配置)禁止覆盖受限制的标识符 (NaN、Infinity、undefined)
    "no-undef": 2, //禁用未声明的变量，除非它们在 /*global */ 注释中被提到
    "no-use-before-define": [1, "nofunc"], //(非推荐配置)禁止在变量定义之前使用它们

    // ====== Node.js and CommonJS (这些规则是关于Node.js 或 在浏览器中使用CommonJS 的) ======

    "no-mixed-requires": 0, //(非推荐配置)禁止混合常规变量声明和 require 调用

    // ====== Stylistic Issues (这些规则是关于风格指南的，而且是非常主观的) ======

    "key-spacing": [1, { "beforeColon": false, "afterColon": true }], //(非推荐配置)强制在对象字面量的属性中键和值之间使用一致的间距,例如{ "foo": 42 }
    "keyword-spacing": 1, //(非推荐配置)建议在关键字(例如if,else等)前后加空格
    //"no-lonely-if" : 1,                       //(非推荐配置)禁止 if 作为唯一的语句出现在 else 语句中
    "no-mixed-spaces-and-tabs": 1, //禁止空格和 tab 的混合缩进
    //"no-underscore-dangle" : 1,               //(非推荐配置)禁止标识符中有悬空下划线
    "quotes": [1, "single", { "avoidEscape": true }], //(非推荐配置)建议使用单引号
    "semi": ["error", "always"], //(非推荐配置)要求使用;结尾
    "space-infix-ops": 1, //(非推荐配置)要求操作符周围有空格

    // ====== ECMAScript 6 (这些规则是关于风格指南的，而且是非常主观的) ======

    "constructor-super": 2, //要求在构造函数中有 super() 的调用
    "no-class-assign": 2, //禁止修改类声明的变量
    "no-const-assign": 2, //禁止修改 const 声明的变量
    "no-dupe-class-members": 2, //禁止类成员中出现重复的名称
    "no-new-symbol": 2, //disallow new operators with the Symbol object
    "no-this-before-super": 2, //禁止在构造函数中，在调用 super() 之前使用 this 或 super
    "require-yield": 2, //要求 generator 函数内有 yield

    // ====== react ======

    "react/prop-types": 0, //不要求进行props的validation

    // ====== vue ======

    "vue/attribute-hyphenation": 0, //不校验 https://eslint.vuejs.org/rules/attribute-hyphenation.html
    "vue/max-attributes-per-line": 0, //不校验 https://eslint.vuejs.org/rules/max-attributes-per-line.html
    "vue/singleline-html-element-content-newline": 0, //不校验 https://eslint.vuejs.org/rules/singleline-html-element-content-newline.html
    "vue/multi-word-component-names": 0,
    "vue/no-mutating-props": 0,
    "vue/order-in-components": [
      "error",
      {
        "order": [
          "el",
          "name",
          "key",
          "parent",
          "functional",
          ["delimiters", "comments"],
          "extends",
          "mixins",
          ["components", "directives", "filters"],
          ["provide", "inject"],
          "ROUTER_GUARDS",
          "layout",
          "middleware",
          "validate",
          "scrollToTop",
          "transition",
          "loading",
          "inheritAttrs",
          "model",
          ["props", "propsData"],
          "emits",
          "setup",
          "asyncData",
          "data",
          "fetch",
          "head",
          "computed",
          "watch",
          "watchQuery",
          "LIFECYCLE_HOOKS",
          "methods",
          ["template", "render"],
          "renderError"
        ]
      }
    ],

    // ====== 其他 ======

    "object-shorthand": 0, // https://eslint.org/docs/latest/rules/object-shorthand
    "prefer-const": 0, // https://eslint.org/docs/latest/rules/prefer-const
    "sort-imports": 0, // https://eslint.org/docs/latest/rules/sort-imports
    "brace-style": 0, // https://eslint.org/docs/latest/rules/brace-style
    "n/handle-callback-err": 0,
    "import/order": 0,
    "quote-props": 0,
    "linebreak-style": 0,
    "import/prefer-default-export": 0,
    "no-useless-escape": 0,
    "max-len": 0,

    // ====== 跟 Prettier 冲突，所以不做校验  ======

    "indent": 0,
    "vue/html-self-closing": 0,
    "arrow-parens": 0,
    "operator-linebreak": 0,
    // "comma-dangle": ["warn", "always-multiline"], //(非推荐配置)建议数组或对象最后使用多余的逗号
    "comma-dangle": 0,
    "vue/multiline-html-element-content-newline": 0,
    "vue/html-closing-bracket-newline": 0,

    // ====== Strict Mode (该规则与使用严格模式和严格模式指令有关) ======

    "strict": 0
  }
}
